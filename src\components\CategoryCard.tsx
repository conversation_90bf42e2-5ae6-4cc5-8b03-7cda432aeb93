import React from 'react';
import { Link } from 'react-router-dom';
import { Category } from '../types';

interface CategoryCardProps {
  category: Category;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {
  return (
    <Link
      to={`/category/${category.id}`}
      className="block group overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300 bg-white"
    >
      <div className="relative h-48 overflow-hidden">
        <img
          src={category.image || category.image_url}
          alt={category.name}
          className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        <h3 className="absolute bottom-4 left-4 text-white text-xl font-semibold">{category.name}</h3>
      </div>
      <div className="p-4">
        <p className="text-gray-600 text-sm">{category.description}</p>
        <div className="mt-3 flex justify-between items-center">
          <span className="text-primary-600 font-medium text-sm">Browse Products</span>
          <span className="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full">
            Fresh Items
          </span>
        </div>
      </div>
    </Link>
  );
};

export default CategoryCard;