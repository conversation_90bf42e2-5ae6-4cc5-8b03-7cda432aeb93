import React from 'react';
import { ShoppingCart, Plus, Minus } from 'lucide-react';
import { Product } from '../types';
import { useCart } from '../context/CartContext';

interface ProductCardProps {
  product: Product;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const { addToCart, items, updateQuantity } = useCart();

  const cartItem = items.find(item => item.product.id === product.id);
  const quantity = cartItem ? cartItem.quantity : 0;

  // Check if product is out of stock
  const isOutOfStock = !product.is_available ||
    (product.stock_quantity !== undefined && product.stock_quantity <= 0) ||
    (product.stock !== undefined && product.stock <= 0);

  const handleAddToCart = () => {
    if (!isOutOfStock) {
      addToCart(product);
    }
  };

  const handleIncreaseQuantity = () => {
    if (!isOutOfStock) {
      addToCart(product);
    }
  };

  const handleDecreaseQuantity = () => {
    if (quantity > 0) {
      updateQuantity(product.id, quantity - 1);
    }
  };

  return (
    <div className="group bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300"
    >
      <div className="relative h-40 sm:h-48 overflow-hidden">
        <img
          src={product.image || product.image_url}
          alt={product.name}
          className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
        />

        {(!product.is_available || (product.stock_quantity <= 0 || product.stock <= 0)) && (
          <div className="absolute top-0 right-0 left-0 bg-red-500 text-white text-center py-1 text-xs">
            Out of Stock
          </div>
        )}

        {product.is_available && ((product.stock_quantity > 0 && product.stock_quantity <= 10) || (product.stock > 0 && product.stock <= 10)) && (
          <div className="absolute top-0 right-0 left-0 bg-orange-500 text-white text-center py-1 text-xs">
            Low Stock: Only {product.stock_quantity || product.stock} left
          </div>
        )}
      </div>

      <div className="p-3 sm:p-4">
        <div className="flex justify-between items-start">
          <h3 className="text-gray-800 font-medium text-sm sm:text-base line-clamp-2">{product.name}</h3>
          <span className="text-xs text-gray-500 ml-1 flex-shrink-0">{product.unit}</span>
        </div>

        <div className="mt-1 flex justify-between items-center">
          <p className="text-primary-600 font-bold">₹{product.price}</p>
        </div>

        <div className="mt-2 sm:mt-3">
          {isOutOfStock ? (
            <button
              disabled
              className="w-full py-1.5 px-2 sm:px-3 bg-gray-200 text-gray-500 rounded-md flex items-center justify-center space-x-1 sm:space-x-2 text-xs sm:text-sm cursor-not-allowed"
            >
              <ShoppingCart className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>Out of Stock</span>
            </button>
          ) : quantity === 0 ? (
            <button
              onClick={handleAddToCart}
              className="w-full py-1.5 px-2 sm:px-3 bg-primary-100 hover:bg-primary-200 text-primary-700 rounded-md flex items-center justify-center space-x-1 sm:space-x-2 transition-colors group-hover:bg-primary-600 group-hover:text-white text-xs sm:text-sm"
            >
              <ShoppingCart className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>Add to Cart</span>
            </button>
          ) : (
            <div className="flex items-center justify-between bg-primary-100 rounded-md">
              <button
                onClick={handleDecreaseQuantity}
                className="p-1 sm:p-1.5 text-primary-700 hover:bg-primary-200 rounded-l-md transition-colors"
              >
                <Minus className="w-3 h-3 sm:w-4 sm:h-4" />
              </button>
              <span className="text-primary-800 font-medium px-1 sm:px-2 text-sm">{quantity}</span>
              <button
                onClick={handleIncreaseQuantity}
                disabled={isOutOfStock}
                className={`p-1 sm:p-1.5 rounded-r-md transition-colors ${
                  isOutOfStock
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-primary-700 hover:bg-primary-200'
                }`}
              >
                <Plus className="w-3 h-3 sm:w-4 sm:h-4" />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCard;