import React from 'react';
import { Trash, Plus, Minus } from 'lucide-react';
import { CartItem as CartItemType } from '../types';
import { useCart } from '../context/CartContext';

interface CartItemProps {
  item: CartItemType;
}

const CartItem: React.FC<CartItemProps> = ({ item }) => {
  const { updateQuantity, removeFromCart } = useCart();
  const { product, quantity } = item;

  // Check if product is out of stock
  const isOutOfStock = !product.is_available ||
    (product.stock_quantity !== undefined && product.stock_quantity <= 0) ||
    (product.stock !== undefined && product.stock <= 0);

  const handleIncrease = () => {
    if (!isOutOfStock) {
      updateQuantity(product.id, quantity + 1);
    }
  };

  const handleDecrease = () => {
    if (quantity > 1) {
      updateQuantity(product.id, quantity - 1);
    } else {
      removeFromCart(product.id);
    }
  };

  return (
    <div className="flex py-4 border-b border-gray-200">
      <div className="h-20 w-20 flex-shrink-0 overflow-hidden rounded-md">
        <img
          src={product.image}
          alt={product.name}
          className="h-full w-full object-cover"
        />
      </div>

      <div className="ml-4 flex flex-1 flex-col">
        <div className="flex justify-between text-base font-medium text-gray-900">
          <h3 className="text-sm">{product.name}</h3>
          <p className="text-sm ml-4">₹{product.price * quantity}</p>
        </div>
        <div className="mt-1 flex items-center justify-between">
          <p className="text-xs text-gray-500">{product.unit}</p>
          {isOutOfStock && (
            <span className="text-xs text-red-500 font-medium">Out of Stock</span>
          )}
        </div>

        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center border border-gray-300 rounded-md">
            <button
              onClick={handleDecrease}
              className="px-2 py-1 text-gray-600 hover:bg-gray-100"
            >
              <Minus className="w-3 h-3" />
            </button>
            <span className="px-2 py-1 text-gray-700 text-sm">{quantity}</span>
            <button
              onClick={handleIncrease}
              disabled={isOutOfStock}
              className={`px-2 py-1 ${
                isOutOfStock
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <Plus className="w-3 h-3" />
            </button>
          </div>

          <button
            onClick={() => removeFromCart(product.id)}
            className="text-red-500 hover:text-red-700"
          >
            <Trash className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default CartItem;