import React, { useState, useEffect } from 'react';
import { ShoppingCart, Search, User, X } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import SearchBar from './SearchBar';

const Header: React.FC = () => {
  const { getTotalItems, toggleCart } = useCart();
  const { isAuthenticated, customer } = useAuth();
  const [showSearch, setShowSearch] = useState(false);
  // Use local image path
  const logoUrl = '/image/dhanamlogo.png';

  useEffect(() => {
    console.log("Header - Using local logo path:", logoUrl);
  }, []);

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          <Link to="/" className="logo-link flex items-center">
            <img
              src={logoUrl}
              alt="Dhanam Supermarket"
              className="h-24 w-auto object-contain"
            />
            <span className="ml-2 font-bold text-primary-600 text-lg">Dhanam</span>
          </Link>

          <div className="flex items-center space-x-4">
            {isAuthenticated && customer && (
              <Link
                to="/profile"
                className="nav-button flex items-center text-sm text-gray-700 hover:text-primary-600"
                title={`Logged in as ${customer.name} (${customer.whatsapp_number})`}
              >
                <div className="relative">
                  <User className="w-4 h-4 mr-1" />
                  <span className="absolute -top-1 -right-1 bg-primary-600 w-2 h-2 rounded-full"></span>
                </div>
                <span className="hidden md:inline">{customer.name}</span>
                <span className="hidden md:inline text-xs text-gray-500 ml-1">
                  ({customer.whatsapp_number})
                </span>
              </Link>
            )}

            <button
              onClick={() => setShowSearch(!showSearch)}
              className="nav-button p-2 rounded-full hover:bg-primary-50 transition-colors"
              aria-label="Search"
            >
              <Search className="w-5 h-5 text-gray-700" />
            </button>

            <button
              onClick={toggleCart}
              className="nav-button p-2 rounded-full hover:bg-primary-50 transition-colors relative"
              aria-label="Cart"
            >
              <ShoppingCart className="w-5 h-5 text-gray-700" />
              {getTotalItems() > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary-600 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                  {getTotalItems()}
                </span>
              )}
            </button>
          </div>
        </div>

        {showSearch && (
          <div className="px-4 py-3 border-t border-gray-100 animate-fadeIn">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <SearchBar onClose={() => setShowSearch(false)} />
              </div>
              <button
                onClick={() => setShowSearch(false)}
                className="nav-button ml-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="Close search"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;




