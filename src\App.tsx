import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { CartProvider } from './context/CartContext';
import { AuthProvider } from './context/AuthContext';
import Header from './components/Header';
import ProtectedRoute from './components/ProtectedRoute';
import CategoriesPage from './pages/CategoriesPage';
import CategoryPage from './pages/CategoryPage';
import SubcategoryPage from './pages/SubcategoryPage';
import CheckoutPage from './pages/CheckoutPage';
import SearchPage from './pages/SearchPage';
import ProfilePage from './pages/ProfilePage';
import PhoneAuthPage from './pages/PhoneAuthPage';
import TestAuthPage from './pages/TestAuthPage';
import AuthRequiredPage from './pages/AuthRequiredPage';
import OrderConfirmationPage from './pages/OrderConfirmationPage';
import CartSidebar from './components/CartSidebar';

function App() {
  return (
    <CartProvider>
      <BrowserRouter>
        <AuthProvider>
          <div className="flex flex-col min-h-screen">
            {/* Only show header on protected routes */}
            <Routes>
              <Route path="/auth/*" element={null} />
              <Route path="/auth-required" element={null} />
              <Route path="/*" element={<Header />} />
            </Routes>
            <main className="flex-grow">
              <Routes>
                {/* Public routes */}
                <Route path="/auth/:phoneNumber" element={<PhoneAuthPage />} />
                <Route path="/auth-required" element={<AuthRequiredPage />} />
                <Route path="/test-auth/:phoneNumber" element={<TestAuthPage />} />

                {/* Protected routes - require authentication */}
                <Route path="/" element={
                  <ProtectedRoute>
                    <CategoriesPage />
                  </ProtectedRoute>
                } />
                <Route path="/categories" element={
                  <ProtectedRoute>
                    <CategoriesPage />
                  </ProtectedRoute>
                } />
                <Route path="/category/:categoryId" element={
                  <ProtectedRoute>
                    <CategoryPage />
                  </ProtectedRoute>
                } />
                <Route path="/subcategory/:subcategoryId" element={
                  <ProtectedRoute>
                    <SubcategoryPage />
                  </ProtectedRoute>
                } />
                <Route path="/checkout" element={
                  <ProtectedRoute>
                    <CheckoutPage />
                  </ProtectedRoute>
                } />
                <Route path="/search" element={
                  <ProtectedRoute>
                    <SearchPage />
                  </ProtectedRoute>
                } />
                <Route path="/profile" element={
                  <ProtectedRoute>
                    <ProfilePage />
                  </ProtectedRoute>
                } />
                <Route path="/order-confirmation/:orderId" element={
                  <ProtectedRoute>
                    <OrderConfirmationPage />
                  </ProtectedRoute>
                } />
              </Routes>
            </main>
            {/* Only show cart sidebar on protected routes */}
            <Routes>
              <Route path="/auth/*" element={null} />
              <Route path="/auth-required" element={null} />
              <Route path="/*" element={<CartSidebar />} />
            </Routes>
          </div>
        </AuthProvider>
      </BrowserRouter>
    </CartProvider>
  );
}

export default App;