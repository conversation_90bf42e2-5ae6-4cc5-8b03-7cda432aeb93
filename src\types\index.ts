export interface Category {
  id: string;
  name: string;
  image_url: string;
  description?: string;
  created_at?: string;
}

export interface Subcategory {
  id: string;
  category_id: string;
  name: string;
  image_url: string;
  created_at?: string;
}

export interface Product {
  id: string;
  subcategory_id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  stock_quantity: number;
  is_available: boolean;
  unit?: string; // For backward compatibility
  category?: string; // For backward compatibility
  stock?: number; // For backward compatibility
  image?: string; // For backward compatibility
  created_at?: string;
  updated_at?: string;
}

export interface CartItem {
  product: Product;
  quantity: number;
}

export interface Order {
  id?: string;
  customer_id: string;
  total_amount: number;
  payment_method: string;
  payment_status: string;
  address_id: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

export interface OrderItem {
  id?: string;
  order_id: string;
  product_name: string;
  product_description: string;
  product_image: string;
  quantity: number;
  price: number;
}