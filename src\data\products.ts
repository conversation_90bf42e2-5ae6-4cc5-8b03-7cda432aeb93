import { Product } from '../types';

export const products: Product[] = [
  // Fruits
  {
    id: 'f1',
    name: 'Alphonso Mango',
    price: 399,
    image: 'https://images.pexels.com/photos/918643/pexels-photo-918643.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'fruits',
    unit: '1 kg',
    stock: 25,
    description: 'Premium Alphonso mangoes from Ratnagiri, Maharashtra. Sweet and aromatic.'
  },
  {
    id: 'f2',
    name: 'Banana - Robusta',
    price: 69,
    image: 'https://images.pexels.com/photos/1093038/pexels-photo-1093038.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'fruits',
    unit: '1 dozen',
    stock: 50,
    description: 'Fresh and ripe robusta bananas. Rich in potassium and fiber.'
  },
  {
    id: 'f3',
    name: 'Pomegranate',
    price: 199,
    image: 'https://images.pexels.com/photos/1420338/pexels-photo-1420338.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'fruits',
    unit: '1 kg',
    stock: 30,
    description: 'Fresh pomegranates with juicy red arils. Rich in antioxidants.'
  },
  {
    id: 'f4',
    name: 'Kashmiri Apple',
    price: 220,
    image: 'https://images.pexels.com/photos/1510392/pexels-photo-1510392.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'fruits',
    unit: '1 kg',
    stock: 40,
    description: 'Sweet and crunchy apples from the valleys of Kashmir.'
  },

  // Vegetables
  {
    id: 'v1',
    name: 'Tomato - Local',
    price: 60,
    image: 'https://images.pexels.com/photos/533280/pexels-photo-533280.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'vegetables',
    unit: '1 kg',
    stock: 45,
    description: 'Fresh, ripe tomatoes. Perfect for curries and salads.'
  },
  {
    id: 'v2',
    name: 'Potato',
    price: 30,
    image: 'https://images.pexels.com/photos/2286776/pexels-photo-2286776.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'vegetables',
    unit: '1 kg',
    stock: 100,
    description: 'Fresh potatoes. Versatile and perfect for various Indian dishes.'
  },
  {
    id: 'v3',
    name: 'Lady Finger (Bhindi)',
    price: 80,
    image: 'https://images.pexels.com/photos/4099137/pexels-photo-4099137.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'vegetables',
    unit: '500 g',
    stock: 25,
    description: 'Fresh lady fingers. Perfect for curries and bhindi masala.'
  },
  {
    id: 'v4',
    name: 'Onion',
    price: 35,
    image: 'https://images.pexels.com/photos/4197493/pexels-photo-4197493.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'vegetables',
    unit: '1 kg',
    stock: 150,
    description: 'Fresh red onions. Essential for Indian cooking.'
  },

  // Dairy
  {
    id: 'd1',
    name: 'Amul Milk',
    price: 68,
    image: 'https://images.pexels.com/photos/5946642/pexels-photo-5946642.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'dairy',
    unit: '1 liter',
    stock: 60,
    description: 'Pasteurized toned milk. Rich in calcium and protein.'
  },
  {
    id: 'd2',
    name: 'Paneer - Fresh',
    price: 80,
    image: 'https://images.pexels.com/photos/4193851/pexels-photo-4193851.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'dairy',
    unit: '200 g',
    stock: 30,
    description: 'Fresh homemade paneer. Perfect for curries and snacks.'
  },
  {
    id: 'd3',
    name: 'Amul Butter',
    price: 55,
    image: 'https://images.pexels.com/photos/236781/pexels-photo-236781.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'dairy',
    unit: '100 g',
    stock: 45,
    description: 'Creamy and delicious Amul butter. Perfect for bread and cooking.'
  },
  {
    id: 'd4',
    name: 'Farm Fresh Eggs',
    price: 78,
    image: 'https://images.pexels.com/photos/6287274/pexels-photo-6287274.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'dairy',
    unit: '6 pcs',
    stock: 100,
    description: 'Fresh and nutritious eggs from free-range hens.'
  },

  // Spices
  {
    id: 's1',
    name: 'Turmeric Powder',
    price: 45,
    image: 'https://images.pexels.com/photos/4197439/pexels-photo-4197439.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'spices',
    unit: '100 g',
    stock: 50,
    description: 'Pure and organic turmeric powder. Essential for Indian cooking.'
  },
  {
    id: 's2',
    name: 'Red Chilli Powder',
    price: 50,
    image: 'https://images.pexels.com/photos/8469852/pexels-photo-8469852.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'spices',
    unit: '100 g',
    stock: 45,
    description: 'Premium quality red chilli powder for that perfect spicy kick.'
  },
  {
    id: 's3',
    name: 'Garam Masala',
    price: 70,
    image: 'https://images.pexels.com/photos/5342326/pexels-photo-5342326.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'spices',
    unit: '100 g',
    stock: 40,
    description: 'Aromatic blend of ground spices. Perfect for curries and biryanis.'
  },
  {
    id: 's4',
    name: 'Cumin Seeds (Jeera)',
    price: 60,
    image: 'https://images.pexels.com/photos/4198836/pexels-photo-4198836.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'spices',
    unit: '100 g',
    stock: 55,
    description: 'Premium quality cumin seeds. Aromatic and flavorful.'
  },

  // Grains
  {
    id: 'g1',
    name: 'Basmati Rice',
    price: 160,
    image: 'https://images.pexels.com/photos/7439529/pexels-photo-7439529.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'grains',
    unit: '1 kg',
    stock: 70,
    description: 'Premium long-grain basmati rice. Aromatic and fluffy when cooked.'
  },
  {
    id: 'g2',
    name: 'Toor Dal',
    price: 120,
    image: 'https://images.pexels.com/photos/7439560/pexels-photo-7439560.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'grains',
    unit: '1 kg',
    stock: 60,
    description: 'Premium quality toor dal. Perfect for daily cooking.'
  },
  {
    id: 'g3',
    name: 'Whole Wheat Atta',
    price: 55,
    image: 'https://images.pexels.com/photos/7439491/pexels-photo-7439491.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'grains',
    unit: '1 kg',
    stock: 80,
    description: 'Stone-ground whole wheat flour. Perfect for soft rotis.'
  },
  {
    id: 'g4',
    name: 'Moong Dal',
    price: 130,
    image: 'https://images.pexels.com/photos/7439557/pexels-photo-7439557.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'grains',
    unit: '1 kg',
    stock: 55,
    description: 'Premium quality moong dal. Nutritious and easy to digest.'
  },

  // Snacks
  {
    id: 'sn1',
    name: 'Haldiram Aloo Bhujia',
    price: 85,
    image: 'https://images.pexels.com/photos/1618917/pexels-photo-1618917.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'snacks',
    unit: '400 g',
    stock: 40,
    description: 'Crispy and spicy potato noodles. Perfect tea-time snack.'
  },
  {
    id: 'sn2',
    name: 'Parle-G Biscuits',
    price: 25,
    image: 'https://images.pexels.com/photos/1586942/pexels-photo-1586942.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'snacks',
    unit: '250 g',
    stock: 100,
    description: 'Classic glucose biscuits loved by all generations.'
  },
  {
    id: 'sn3',
    name: 'MTR Mixture',
    price: 60,
    image: 'https://images.pexels.com/photos/1640775/pexels-photo-1640775.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'snacks',
    unit: '200 g',
    stock: 35,
    description: 'Crunchy and spicy South Indian mixture. Perfect tea-time snack.'
  },
  {
    id: 'sn4',
    name: 'Samosa - 2 Pcs',
    price: 30,
    image: 'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'snacks',
    unit: '2 pcs',
    stock: 25,
    description: 'Crispy pastry filled with spiced potatoes and peas.'
  },

  // Beverages
  {
    id: 'b1',
    name: 'Tata Tea Premium',
    price: 140,
    image: 'https://images.pexels.com/photos/1417945/pexels-photo-1417945.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'beverages',
    unit: '500 g',
    stock: 65,
    description: 'Premium blend of Assam and Darjeeling tea leaves.'
  },
  {
    id: 'b2',
    name: 'Nescafe Classic',
    price: 265,
    image: 'https://images.pexels.com/photos/312418/pexels-photo-312418.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'beverages',
    unit: '200 g',
    stock: 45,
    description: 'Rich and aromatic instant coffee. Perfect to start your day.'
  },
  {
    id: 'b3',
    name: 'Real Fruit Juice - Mango',
    price: 120,
    image: 'https://images.pexels.com/photos/96974/pexels-photo-96974.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'beverages',
    unit: '1 liter',
    stock: 30,
    description: 'Refreshing mango fruit juice. No added preservatives.'
  },
  {
    id: 'b4',
    name: 'Thumbs Up',
    price: 40,
    image: 'https://images.pexels.com/photos/2775860/pexels-photo-2775860.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'beverages',
    unit: '600 ml',
    stock: 75,
    description: 'Strong, refreshing cola with a distinct taste.'
  },

  // Household
  {
    id: 'h1',
    name: 'Surf Excel',
    price: 180,
    image: 'https://images.pexels.com/photos/5217894/pexels-photo-5217894.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'household',
    unit: '1 kg',
    stock: 50,
    description: 'Advanced detergent powder for tough stain removal.'
  },
  {
    id: 'h2',
    name: 'Harpic Toilet Cleaner',
    price: 76,
    image: 'https://images.pexels.com/photos/6996014/pexels-photo-6996014.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'household',
    unit: '500 ml',
    stock: 45,
    description: 'Powerful toilet cleaner that kills 99.9% germs.'
  },
  {
    id: 'h3',
    name: 'Colin Glass Cleaner',
    price: 99,
    image: 'https://images.pexels.com/photos/6407705/pexels-photo-6407705.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'household',
    unit: '500 ml',
    stock: 40,
    description: 'Effective glass and surface cleaner for a streak-free shine.'
  },
  {
    id: 'h4',
    name: 'Good Knight Mosquito Repellent',
    price: 72,
    image: 'https://images.pexels.com/photos/6098038/pexels-photo-6098038.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'household',
    unit: '1 refill',
    stock: 60,
    description: 'Effective mosquito repellent for a peaceful sleep.'
  }
];

export const getProductsByCategory = (categoryId: string): Product[] => {
  return products.filter(product => product.category === categoryId);
};

export const searchProducts = (query: string): Product[] => {
  const lowercaseQuery = query.toLowerCase();
  return products.filter(
    product => 
      product.name.toLowerCase().includes(lowercaseQuery) || 
      product.description.toLowerCase().includes(lowercaseQuery)
  );
};