import React, { useState, useEffect } from 'react';
import { Link, Navigate, useNavigate } from 'react-router-dom';
import { ChevronRight, CreditCard, ShoppingBag, Home, TruckIcon, AlertCircle, CheckCircle } from 'lucide-react';
import CartItem from '../components/CartItem';
import AddressManager from '../components/AddressManager';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import { createOrder } from '../lib/api';
import { initRazorpay, createRazorpayOrder, openRazorpayCheckout } from '../lib/razorpay';

const CheckoutPage: React.FC = () => {
  const navigate = useNavigate();
  const { items, getTotalPrice, clearCart } = useCart();
  const { isAuthenticated, customer, addresses, loading } = useAuth();
  const [activeStep, setActiveStep] = useState<'address' | 'payment'>('address');
  const [selectedAddressId, setSelectedAddressId] = useState<string>('');
  const [formData, setFormData] = useState({
    paymentMethod: 'cod'
  });
  const [orderProcessing, setOrderProcessing] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderError, setOrderError] = useState<string | null>(null);
  const [razorpayLoaded, setRazorpayLoaded] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [paymentProcessing, setPaymentProcessing] = useState(false);

  // Set default selected address if available
  useEffect(() => {
    if (addresses.length > 0) {
      // Try to find default address first
      const defaultAddress = addresses.find(addr => addr.is_default);
      if (defaultAddress) {
        setSelectedAddressId(defaultAddress.id);
      } else {
        // Otherwise use the first address
        setSelectedAddressId(addresses[0].id);
      }
    }
  }, [addresses]);

  // Load Razorpay script
  useEffect(() => {
    const loadRazorpay = async () => {
      const isLoaded = await initRazorpay();
      setRazorpayLoaded(isLoaded);
      if (!isLoaded) {
        console.error('Failed to load Razorpay script');
      }
    };

    loadRazorpay();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectAddress = (addressId: string) => {
    setSelectedAddressId(addressId);
  };

  const handleContinueToPayment = () => {
    if (selectedAddressId) {
      setActiveStep('payment');
    } else {
      alert('Please select or add a delivery address');
    }
  };

  const handleRazorpayPayment = async (orderId: string, totalAmount: number) => {
    if (!customer) return;

    setPaymentProcessing(true);

    try {
      console.log('Starting Razorpay payment process for order:', orderId, 'amount:', totalAmount);

      // In a real implementation, we would create a Razorpay order via a backend API
      // For our test implementation, we're using a simplified approach
      const razorpayOrder = await createRazorpayOrder(orderId, totalAmount);

      console.log('Razorpay order preparation complete:', razorpayOrder);

      if (!razorpayOrder) {
        console.error('Failed to prepare for Razorpay payment');
        setOrderError('Failed to initialize payment. Please try again.');
        setPaymentProcessing(false);
        return;
      }

      // Open Razorpay checkout
      openRazorpayCheckout(
        {
          id: orderId,
          amount: totalAmount,
          currency: 'INR',
          razorpayOrderId: razorpayOrder.id
        },
        {
          name: customer.name,
          email: customer.email || '',
          phone: customer.whatsapp_number
        },
        // Success callback
        async (paymentId) => {
          console.log('Payment successful:', paymentId);
          console.log('Payment received with ID:', paymentId);

          // With the webhook in place, we don't need to update the order status directly
          // The webhook will handle updating the order status in the database

          // For better UX, we'll still update the local state to show success
          setOrderSuccess(true);
          clearCart();
          setOrderError(null);

          // Redirect to order confirmation page using the stored orderId
          if (orderId) {
            navigate(`/order-confirmation/${orderId}`);
          }

          setPaymentProcessing(false);
        },
        // Failure callback
        async (error) => {
          console.error('Razorpay payment failed:', error);

          // The webhook will handle updating the order status in the database
          // We just need to show an error message to the user

          // Extract error details for better user feedback
          const errorDescription = error.error?.description || 'Unknown error';
          const errorCode = error.error?.code || '';
          const errorSource = error.error?.source || '';
          const errorStep = error.error?.step || '';

          console.log('Payment failure details:', {
            description: errorDescription,
            code: errorCode,
            source: errorSource,
            step: errorStep
          });

          // Show a user-friendly error message
          setOrderError(`Payment failed: ${errorDescription}. Please try again or choose a different payment method.`);
          setPaymentProcessing(false);
        }
      );
    } catch (error) {
      console.error('Error processing Razorpay payment:', error);
      setOrderError('An unexpected error occurred. Please try again.');
      setPaymentProcessing(false);
    }
  };

  const handleSubmitPayment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!customer || !selectedAddressId) {
      setOrderError('Missing customer or address information');
      return;
    }

    try {
      setOrderProcessing(true);
      setOrderError(null);

      // Calculate final total amount
      const totalAmount = getTotalPrice();

      // Create the order in the database
      // Make sure we're using 'cod' or another value that maps to 'ONLINE' in the backend
      const paymentMethodForAPI = formData.paymentMethod === 'cod' ? 'cod' : 'online';
      const result = await createOrder(
        customer.id,
        selectedAddressId,
        items,
        paymentMethodForAPI,
        totalAmount
      );

      if (result.success && result.orderId) {
        setOrderId(result.orderId);

        // If payment method is Razorpay, initiate Razorpay payment
        if (formData.paymentMethod === 'razorpay') {
          if (!razorpayLoaded) {
            setOrderError('Payment gateway is not available. Please try again or choose a different payment method.');
            setOrderProcessing(false);
            return;
          }

          // Process Razorpay payment
          await handleRazorpayPayment(result.orderId, totalAmount);
        } else {
          // For COD and other payment methods
          setOrderSuccess(true);
          clearCart();

          // Send WhatsApp notification for COD orders
          if (formData.paymentMethod === 'cod') {
            try {
              // Call the COD notification function
              const response = await fetch('https://phmbblwthsregggkwbfo.supabase.co/functions/v1/cod-notification', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  orderId: result.orderId,
                  customerId: customer.id,
                  totalAmount: totalAmount
                })
              });

              console.log('COD notification response:', await response.json());
            } catch (notificationError) {
              console.error('Error sending COD notification:', notificationError);
              // Don't block the order process if notification fails
            }
          }

          // Redirect to order confirmation page
          navigate(`/order-confirmation/${result.orderId}`);
        }
      } else {
        setOrderError(result.message);
      }
    } catch (error) {
      console.error('Error placing order:', error);
      setOrderError('An unexpected error occurred. Please try again.');
    } finally {
      if (formData.paymentMethod !== 'razorpay') {
        setOrderProcessing(false);
      }
    }
  };

  // Redirect to home if not authenticated
  if (!isAuthenticated && !loading) {
    return <Navigate to="/" />;
  }

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="text-center">
          <ShoppingBag className="mx-auto h-12 w-12 text-gray-400" />
          <h2 className="mt-2 text-lg font-medium text-gray-900">Your cart is empty</h2>
          <p className="mt-1 text-sm text-gray-500">Add some items to your cart before checking out</p>
          <div className="mt-6">
            <Link
              to="/"
              className="inline-flex justify-center px-4 py-2 border border-transparent
              rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
            >
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20">
      <div className="bg-primary-50 py-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600 mb-2">
            <Link to="/" className="hover:text-primary-600">Home</Link>
            <ChevronRight className="w-4 h-4 mx-1" />
            <span className="text-gray-800">Checkout</span>
          </div>

          <h1 className="text-3xl font-bold text-gray-800">Checkout</h1>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          <div className="lg:w-2/3">
            {/* Checkout Steps */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
              <div className="p-4 md:p-6">
                <div className="flex items-center mb-6">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${activeStep === 'address' ? 'bg-primary-600 text-white' : 'bg-primary-100 text-primary-700'}`}>
                    <Home className="w-4 h-4" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-800">Delivery Address</h3>
                    <p className="text-sm text-gray-500">Enter your delivery details</p>
                  </div>
                </div>

                {activeStep === 'address' && (
                  <div>
                    {/* Customer Information */}
                    <div className="mb-6 p-4 bg-primary-50 rounded-lg">
                      <h4 className="text-md font-medium text-gray-800 mb-2">Customer Information</h4>
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div>
                          <p className="text-gray-700"><span className="font-medium">Name:</span> {customer?.name}</p>
                          <p className="text-gray-700"><span className="font-medium">Phone:</span> {customer?.whatsapp_number}</p>
                          {customer?.email && (
                            <p className="text-gray-700"><span className="font-medium">Email:</span> {customer?.email}</p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Address Manager */}
                    <AddressManager
                      onSelectAddress={handleSelectAddress}
                      selectedAddressId={selectedAddressId}
                    />

                    <div className="mt-6 text-right">
                      <button
                        type="button"
                        onClick={handleContinueToPayment}
                        className="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
                        disabled={!selectedAddressId}
                      >
                        Continue to Payment
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
              <div className="p-4 md:p-6">
                <div className="flex items-center mb-6">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${activeStep === 'payment' ? 'bg-primary-600 text-white' : 'bg-primary-100 text-primary-700'}`}>
                    <CreditCard className="w-4 h-4" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-800">Payment Method</h3>
                    <p className="text-sm text-gray-500">Select your preferred payment method</p>
                  </div>
                </div>

                {activeStep === 'payment' && (
                  <>
                    {orderSuccess ? (
                      <div className="bg-green-50 p-4 rounded-md">
                        <div className="flex items-center">
                          <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
                          <div>
                            <h3 className="text-green-800 font-medium">Order Placed Successfully!</h3>
                            <p className="text-green-700 text-sm mt-1">
                              Thank you for your order. You will receive a confirmation on WhatsApp shortly.
                            </p>
                            {formData.paymentMethod === 'razorpay' && (
                              <p className="text-green-700 text-sm mt-2">
                                Your payment is being processed. A payment confirmation will be sent to your WhatsApp number.
                              </p>
                            )}
                            <p className="text-green-700 text-sm mt-2">
                              You will be redirected to the home page in a few seconds.
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <form onSubmit={handleSubmitPayment}>
                        {orderError && (
                          <div className="bg-red-50 p-4 rounded-md mb-4">
                            <div className="flex items-center">
                              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                              <span className="text-red-700">{orderError}</span>
                            </div>
                          </div>
                        )}

                        <div className="space-y-4">
                          <label className="block border border-gray-200 rounded-md p-3 cursor-pointer hover:bg-gray-50">
                            <div className="flex items-center">
                              <input
                                type="radio"
                                name="paymentMethod"
                                value="cod"
                                checked={formData.paymentMethod === 'cod'}
                                onChange={handleChange}
                                className="h-4 w-4 text-green-600 focus:ring-green-500"
                              />
                              <div className="ml-3">
                                <span className="block text-sm font-medium text-gray-700">Cash on Delivery</span>
                                <span className="block text-xs text-gray-500">Pay when your order arrives</span>
                              </div>
                            </div>
                          </label>

                          <label className="block border border-gray-200 rounded-md p-3 cursor-pointer hover:bg-gray-50">
                            <div className="flex items-center">
                              <input
                                type="radio"
                                name="paymentMethod"
                                value="razorpay"
                                checked={formData.paymentMethod === 'razorpay'}
                                onChange={handleChange}
                                className="h-4 w-4 text-green-600 focus:ring-green-500"
                              />
                              <div className="ml-3 flex items-center">
                                <span className="block text-sm font-medium text-gray-700 mr-2">Razorpay</span>
                                <img
                                  src="https://razorpay.com/assets/razorpay-logo.svg"
                                  alt="Razorpay"
                                  className="h-5"
                                />
                                <span className="block text-xs text-gray-500 ml-2">Pay securely online</span>
                              </div>
                            </div>
                          </label>
                        </div>

                        <div className="mt-6 flex justify-between">
                          <button
                            type="button"
                            onClick={() => setActiveStep('address')}
                            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                            disabled={orderProcessing}
                          >
                            Back to Address
                          </button>

                          <button
                            type="submit"
                            className={`px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors ${
                              orderProcessing || paymentProcessing ? 'opacity-70 cursor-not-allowed' : ''
                            }`}
                            disabled={orderProcessing || paymentProcessing}
                          >
                            {orderProcessing
                              ? 'Processing...'
                              : paymentProcessing
                                ? 'Processing Payment...'
                                : formData.paymentMethod === 'razorpay'
                                  ? 'Pay Now'
                                  : 'Place Order'
                            }
                          </button>
                        </div>
                      </form>
                    )}
                  </>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-4 md:p-6">
                <div className="flex items-center mb-6">
                  <div className="w-8 h-8 rounded-full bg-primary-100 text-primary-700 flex items-center justify-center mr-3">
                    <TruckIcon className="w-4 h-4" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-800">Delivery Information</h3>
                    <p className="text-sm text-gray-500">Estimated delivery time: 24-48 hours</p>
                  </div>
                </div>

                <div className="text-gray-600 text-sm">
                  <p>
                    Your order will be delivered within 24-48 hours of placing the order. You will receive a confirmation
                    on WhatsApp once your order is confirmed, and you'll be able to track its status.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="lg:w-1/3">
            <div className="bg-white rounded-lg shadow-md overflow-hidden sticky top-20">
              <div className="p-4 md:p-6">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Order Summary</h3>

                <div className="max-h-80 overflow-y-auto mb-4">
                  {items.map((item) => (
                    <CartItem key={item.product.id} item={item} />
                  ))}
                </div>

                <div className="border-t border-gray-200 pt-4 space-y-2">
                  <div className="flex justify-between font-semibold">
                    <span className="text-gray-800">Total</span>
                    <span className="text-primary-700 text-lg">₹{getTotalPrice()}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
