# System Patterns

**System Architecture:**

-   The application follows a component-based architecture using React.
-   The backend is powered by Supabase.
-   Tailwind CSS is used for styling.

**Key Technical Decisions:**

-   Using React for the frontend to enable a dynamic and interactive user interface.
-   Using Supabase for the backend to simplify database management and authentication.
-   Using Tailwind CSS for styling to ensure a consistent and maintainable design.

**Design Patterns in Use:**

-   Context API for managing global state (AuthContext, CartContext).
-   Protected routes for securing sensitive pages.
-   Razorpay for payment processing.

**Component Relationships:**

-   The application is composed of various components, including:
    -   Header
    -   Footer
    -   ProductCard
    -   CategoryCard
    -   CartSidebar
    -   etc.
