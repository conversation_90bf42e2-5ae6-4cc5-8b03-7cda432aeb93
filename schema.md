# Database Schema

## Categories Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| name | text | Category name |
| image_url | text | URL to category image |
| created_at | timestamp without time zone | Creation timestamp |

## Subcategories Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| category_id | uuid | Foreign key to categories table |
| name | text | Subcategory name |
| image_url | text | URL to subcategory image |
| created_at | timestamp without time zone | Creation timestamp |

## Products Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| subcategory_id | uuid | Foreign key to subcategories table |
| name | text | Product name |
| description | text | Product description |
| price | numeric | Product price |
| stock | integer | Stock quantity (deprecated) |
| image_url | text | URL to product image |
| created_at | timestamp without time zone | Creation timestamp |
| is_available | boolean | Whether the product is available |
| stock_quantity | integer | Current stock quantity |
| updated_at | timestamp without time zone | Last update timestamp |

## Customers Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| name | text | Customer name |
| whatsapp_number | text | Customer's WhatsApp number |
| email | text | Customer's email address |
| created_at | timestamp without time zone | Creation timestamp |

## Addresses Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| customer_id | uuid | Foreign key to customers table |
| address | text | Delivery address |
| pincode | text | Postal code |
| is_default | boolean | Whether this is the default address |

## Delivery Areas Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| pincode | text | Postal code for delivery area |
| place | text | Name of the delivery area |

## Orders Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| customer_id | uuid | Foreign key to customers table |
| total_amount | numeric | Total order amount |
| payment_method | text | Method of payment |
| cancellation_reason | text | Reason for cancellation (if applicable) |
| created_at | timestamp without time zone | Order creation timestamp |
| updated_at | timestamp without time zone | Last update timestamp |
| payment_status | text | Status of payment |
| address_id | uuid | Foreign key to addresses table |
| razorpay_payment_id | text | Razorpay payment identifier |
| payment_auth_date | timestamp with time zone | Payment authorization date |
| payment_capture_date | timestamp with time zone | Payment capture date |
| payment_failure_date | timestamp with time zone | Payment failure date |
| payment_error | text | Payment error message |
| payment_error_code | text | Payment error code |
| status | text | Order status |

## Order Items Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| order_id | uuid | Foreign key to orders table |
| product_name | text | Name of the product |
| product_description | text | Description of the product |
| product_image | text | URL to product image |
| quantity | integer | Quantity ordered |
| price | numeric | Price per unit |
| cancellation_reason | text | Reason for cancellation (if applicable) |

## Payments Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| order_id | uuid | Foreign key to orders table |
| customer_id | uuid | Foreign key to customers table |
| amount | numeric | Payment amount |
| payment_method | text | Method of payment |
| payment_status | text | Status of payment |
| transaction_id | text | Payment transaction identifier |
| cancellation_reason | text | Reason for cancellation (if applicable) |
| created_at | timestamp without time zone | Payment creation timestamp |
| updated_at | timestamp without time zone | Last update timestamp |

## Return Products Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| order_id | uuid | Foreign key to orders table |
| product_name | text | Name of the product |
| quantity | integer | Quantity returned |
| return_reason | text | Reason for return |
| status | text | Return status |
| requested_at | timestamp without time zone | Return request timestamp |
| processed_at | timestamp without time zone | Return processing timestamp |
| processed_by | uuid | ID of user who processed the return |
| rejection_reason | text | Reason for rejection (if applicable) |
| returned_date | timestamp without time zone | Date when product was returned |

## Users Table

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | uuid | Primary key |
| name | text | User name |
| phone | text | User phone number |
| email | text | User email address |
| password | text | User password (hashed) |
| role | text | User role (admin, staff, etc.) |
| created_at | timestamp without time zone | User creation timestamp |
