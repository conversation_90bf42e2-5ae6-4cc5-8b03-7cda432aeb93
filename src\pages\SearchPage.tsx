import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Search as SearchIcon, Filter, Grid, List } from 'lucide-react';
import ProductCard from '../components/ProductCard';
import { searchProducts as apiSearchProducts } from '../lib/api';
import { Product } from '../types';
import { useCart } from '../context/CartContext';

const SearchPage: React.FC = () => {
  const location = useLocation();
  const query = new URLSearchParams(location.search).get('q') || '';
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortOption, setSortOption] = useState<string>('default');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState(query);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { addToCart } = useCart();

  const performSearch = async (searchTerm: string) => {
    if (!searchTerm) return;

    try {
      setLoading(true);
      let results = await apiSearchProducts(searchTerm);

      // Apply sorting
      switch (sortOption) {
        case 'price-low':
          results = [...results].sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          results = [...results].sort((a, b) => b.price - a.price);
          break;
        case 'name-asc':
          results = [...results].sort((a, b) => a.name.localeCompare(b.name));
          break;
        default:
          // Default sort or no sort
          break;
      }

      setSearchResults(results);
      setError(null);
    } catch (err) {
      console.error('Error searching products:', err);
      setError('Failed to search products. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    performSearch(query);
  }, [query, sortOption]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Update the URL with the new search query
    window.history.pushState({}, '', `/search?q=${encodeURIComponent(searchQuery)}`);
    // Trigger a new search
    performSearch(searchQuery);
  };

  return (
    <div className="min-h-screen pt-16">
      <div className="bg-green-50 py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold text-gray-800">Search Results</h1>
          <p className="text-gray-600 mt-2">
            Showing results for "{query}"
          </p>

          <form onSubmit={handleSearch} className="mt-4 relative max-w-lg">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search for products..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                Search
              </button>
            </div>
          </form>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0 mb-8">
          <div className="flex items-center">
            <span className="text-gray-700 mr-2">Found {searchResults.length} results</span>
          </div>

          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 text-gray-700 hover:text-green-600"
            >
              <Filter className="w-5 h-5" />
              <span>Filter & Sort</span>
            </button>

            <div>
              <select
                value={sortOption}
                onChange={(e) => setSortOption(e.target.value)}
                className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="default">Sort by: Default</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="name-asc">Name: A to Z</option>
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'}`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'}`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {showFilters && (
          <div className="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
            <h3 className="text-lg font-medium text-gray-800 mb-3">Filters</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Categories</p>
                <div className="space-y-2">
                  <label className="inline-flex items-center">
                    <input type="checkbox" className="form-checkbox text-green-600" />
                    <span className="ml-2 text-sm text-gray-700">Fruits</span>
                  </label>
                  <label className="inline-flex items-center">
                    <input type="checkbox" className="form-checkbox text-green-600" />
                    <span className="ml-2 text-sm text-gray-700">Vegetables</span>
                  </label>
                  <label className="inline-flex items-center">
                    <input type="checkbox" className="form-checkbox text-green-600" />
                    <span className="ml-2 text-sm text-gray-700">Dairy</span>
                  </label>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Price Range</p>
                <div className="flex items-center space-x-2">
                  <button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-green-50 hover:border-green-300">
                    Under ₹100
                  </button>
                  <button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-green-50 hover:border-green-300">
                    ₹100 - ₹200
                  </button>
                  <button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-green-50 hover:border-green-300">
                    Above ₹200
                  </button>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={() => setShowFilters(false)}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  Apply Filters
                </button>
              </div>
            </div>
          </div>
        )}

        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {!loading && !error && searchResults.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No products found matching "{query}"</p>
          </div>
        ) : !loading && !error && viewMode === 'grid' ? (
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
            {searchResults.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        ) : !loading && !error ? (
          <div className="space-y-4">
            {searchResults.map((product) => (
              <div key={product.id} className="flex border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow bg-white">
                <img
                  src={product.image || product.image_url}
                  alt={product.name}
                  className="w-32 h-32 object-cover"
                />
                <div className="p-4 flex-1 flex flex-col">
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <h3 className="text-lg font-medium text-gray-800">{product.name}</h3>
                      <span className="text-xs text-gray-500">{product.unit || '1 unit'}</span>
                    </div>
                    <p className="text-gray-600 text-sm mt-1">{product.description}</p>
                  </div>
                  <div className="flex justify-between items-center mt-4">
                    <p className="text-green-700 font-bold">₹{product.price}</p>
                    <button
                      onClick={() => addToCart(product)}
                      className="px-4 py-2 bg-green-100 hover:bg-green-200 text-green-700 rounded-md transition-colors"
                    >
                      Add to Cart
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default SearchPage;