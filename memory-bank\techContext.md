# Tech Context

**Technologies Used:**

-   React
-   TypeScript
-   Tailwind CSS
-   Vite
-   Supabase
-   Razorpay

**Development Setup:**

-   The project uses npm for package management.
-   Vite is used as the development server.

**Technical Constraints:**

-   Limited budget for hosting and infrastructure.
-   Need to ensure the application is performant and scalable.

**Dependencies:**

-   Refer to `package.json` for a complete list of dependencies.
