import { createClient } from '@supabase/supabase-js';

// Supabase client configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://phmbblwthsregggkwbfo.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBobWJibHd0aHNyZWdnZ2t3YmZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1MDkzNDAsImV4cCI6MjA1OTA4NTM0MH0.1Obru9SaNCGLN9wdkhVsuuUjBjH5fIVJOdalByvv7Yo';

// Create Supabase client without type definition to avoid issues with missing tables
export const supabase = createClient(supabaseUrl, supabaseAnonKey);
