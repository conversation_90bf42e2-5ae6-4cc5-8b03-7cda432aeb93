export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      categories: {
        Row: {
          id: string
          name: string
          image_url: string
          created_at?: string
        }
        Insert: {
          id?: string
          name: string
          image_url: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          image_url?: string
          created_at?: string
        }
      }
      subcategories: {
        Row: {
          id: string
          category_id: string
          name: string
          image_url: string
          created_at?: string
        }
        Insert: {
          id?: string
          category_id: string
          name: string
          image_url: string
          created_at?: string
        }
        Update: {
          id?: string
          category_id?: string
          name?: string
          image_url?: string
          created_at?: string
        }
      }
      products: {
        Row: {
          id: string
          subcategory_id: string
          name: string
          description: string
          price: number
          stock?: number
          image_url: string
          created_at?: string
          is_available: boolean
          stock_quantity: number
          updated_at?: string
        }
        Insert: {
          id?: string
          subcategory_id: string
          name: string
          description: string
          price: number
          stock?: number
          image_url: string
          created_at?: string
          is_available?: boolean
          stock_quantity: number
          updated_at?: string
        }
        Update: {
          id?: string
          subcategory_id?: string
          name?: string
          description?: string
          price?: number
          stock?: number
          image_url?: string
          created_at?: string
          is_available?: boolean
          stock_quantity?: number
          updated_at?: string
        }
      }
      customers: {
        Row: {
          id: string
          name: string
          whatsapp_number: string
          email?: string
          created_at?: string
        }
        Insert: {
          id?: string
          name: string
          whatsapp_number: string
          email?: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          whatsapp_number?: string
          email?: string
          created_at?: string
        }
      }
      addresses: {
        Row: {
          id: string
          customer_id: string
          address: string
          pincode: string
          is_default: boolean
        }
        Insert: {
          id?: string
          customer_id: string
          address: string
          pincode: string
          is_default?: boolean
        }
        Update: {
          id?: string
          customer_id?: string
          address?: string
          pincode?: string
          is_default?: boolean
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
