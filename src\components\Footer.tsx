import React, { useEffect } from 'react';
import { Phone, Mail, MapPin, Clock, Facebook, Instagram, Twitter } from 'lucide-react';

const Footer: React.FC = () => {
  // Use local image path
  const logoUrl = '/image/dhanamlogo.png';

  useEffect(() => {
    console.log("Footer - Using local logo path:", logoUrl);
  }, []);

  return (
    <footer className="bg-gray-50 pt-12 pb-8 border-t border-gray-200">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Logo and About */}
          <div>
            <div className="flex items-center mb-4">
              <img
                src={logoUrl}
                alt="Dhanam Supermarket"
                className="h-24 w-auto object-contain"
              />
              <span className="ml-2 font-bold text-primary-600 text-lg">Dhanam</span>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Your one-stop destination for quality groceries and household essentials.
              We deliver fresh products at your doorstep.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="nav-button text-gray-500 hover:text-primary-600 transition-colors">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="nav-button text-gray-500 hover:text-primary-600 transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="#" className="nav-button text-gray-500 hover:text-primary-600 transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-gray-800 font-semibold text-md mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="nav-button text-gray-600 hover:text-primary-600 text-sm transition-colors">
                  About Us
                </a>
              </li>
              <li>
                <a href="#" className="nav-button text-gray-600 hover:text-primary-600 text-sm transition-colors">
                  Contact Us
                </a>
              </li>
              <li>
                <a href="#" className="nav-button text-gray-600 hover:text-primary-600 text-sm transition-colors">
                  Terms & Conditions
                </a>
              </li>
              <li>
                <a href="#" className="nav-button text-gray-600 hover:text-primary-600 text-sm transition-colors">
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="#" className="nav-button text-gray-600 hover:text-primary-600 text-sm transition-colors">
                  FAQs
                </a>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-gray-800 font-semibold text-md mb-4">Categories</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="nav-button text-gray-600 hover:text-primary-600 text-sm transition-colors">
                  Fruits & Vegetables
                </a>
              </li>
              <li>
                <a href="#" className="nav-button text-gray-600 hover:text-primary-600 text-sm transition-colors">
                  Dairy & Eggs
                </a>
              </li>
              <li>
                <a href="#" className="nav-button text-gray-600 hover:text-primary-600 text-sm transition-colors">
                  Rice & Grains
                </a>
              </li>
              <li>
                <a href="#" className="nav-button text-gray-600 hover:text-primary-600 text-sm transition-colors">
                  Spices & Masalas
                </a>
              </li>
              <li>
                <a href="#" className="nav-button text-gray-600 hover:text-primary-600 text-sm transition-colors">
                  Household Essentials
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Information */}
          <div>
            <h3 className="text-gray-800 font-semibold text-md mb-4">Contact Us</h3>
            <ul className="space-y-3">
              <li className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-primary-600 flex-shrink-0" />
                <span className="text-gray-600 text-sm">
                  123 Main Street, Anna Nagar, Chennai - 600040
                </span>
              </li>
              <li className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-primary-600 flex-shrink-0" />
                <span className="text-gray-600 text-sm">+91 98765 43210</span>
              </li>
              <li className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-primary-600 flex-shrink-0" />
                <span className="text-gray-600 text-sm"><EMAIL></span>
              </li>
              <li className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-primary-600 flex-shrink-0" />
                <span className="text-gray-600 text-sm">
                  Open Hours: 8:00 AM - 10:00 PM<br />
                  (All days of the week)
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-gray-200 text-center">
          <p className="text-gray-500 text-sm">
            &copy; {new Date().getFullYear()} Dhanam Supermarket. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;




