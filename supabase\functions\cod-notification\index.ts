import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Enhanced logger function
const log = (message, data) => {
  const logData = {
    timestamp: new Date().toISOString(),
    message,
    data: data
  };
  console.log(JSON.stringify(logData));
};

// Helper function to send WhatsApp message and log the request/response
async function sendWhatsAppMessage(phone, message) {
  const webhookUrl = 'https://bothook.io/v1/public/triggers/webhooks/21fb6429-674b-454b-8121-c50eb89529a3';
  const payload = {
    phone,
    message
  };
  log('Sending WhatsApp message', {
    url: webhookUrl,
    payload
  });
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });
    const responseData = await response.text();
    log('WhatsApp webhook response', {
      status: response.status,
      statusText: response.statusText,
      response: responseData
    });
    if (!response.ok) {
      throw new Error(`WhatsApp webhook failed: ${response.status} ${response.statusText}`);
    }
    return response;
  } catch (error) {
    log('WhatsApp webhook error', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

serve(async (req) => {
  console.log('👍👍👍Request received', req);
  
  // Log incoming request
  log('Incoming COD notification request', {
    method: req.method,
    url: req.url,
    headers: Object.fromEntries(req.headers.entries())
  });
  
  if (req.method === 'OPTIONS') {
    log('OPTIONS request received', null);
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  
  try {
    const requestData = await req.json();
    log('Request data', requestData);
    
    const { orderId, customerId, totalAmount } = requestData;
    
    if (!orderId || !customerId) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Missing required parameters: orderId and customerId'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }
    
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );
    
    // Get customer details
    const { data: customerData, error: customerError } = await supabaseClient
      .from('customers')
      .select('whatsapp_number, name')
      .eq('id', customerId)
      .single();
    
    if (customerError || !customerData?.whatsapp_number) {
      log('Error fetching customer details', {
        error: customerError,
        customerId
      });
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to fetch customer details'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
    
    // Send WhatsApp notification for COD order
    try {
      await sendWhatsAppMessage(
        customerData.whatsapp_number,
        `🛒 Order Placed Successfully!\n\n` +
        `Thank you for your order.\n\n` +
        `Order Details:\n` +
        `----------------\n` +
        `Order ID: ${orderId}\n` +
        `Amount: ₹${totalAmount}\n` +
        `Payment Method: Cash on Delivery\n\n` +
        `Your order has been confirmed and will be processed shortly.`
      );
      
      log('COD order notification sent', {
        phone: customerData.whatsapp_number,
        orderId,
        customerId
      });
      
      return new Response(JSON.stringify({
        success: true,
        message: 'COD order notification sent successfully'
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 200
      });
    } catch (error) {
      log('Error sending COD notification', {
        error: error.message,
        stack: error.stack,
        orderId,
        customerId
      });
      
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to send COD notification',
        error: error.message
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 500
      });
    }
  } catch (error) {
    log('Error processing COD notification request', {
      error: error.message,
      stack: error.stack
    });
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Internal server error',
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
