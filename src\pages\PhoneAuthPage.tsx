import React, { useEffect, useState } from 'react';
import { useParams, Navigate, Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Phone, AlertCircle } from 'lucide-react';

/**
 * This page handles authentication via phone number in the URL
 * It's a simple redirect page that will authenticate the user if the phone number exists
 * and redirect to the home page
 */
const PhoneAuthPage: React.FC = () => {
  const { phoneNumber } = useParams<{ phoneNumber: string }>();
  const { isAuthenticated, loading } = useAuth();
  const [authTimeout, setAuthTimeout] = useState<boolean>(false);

  // Set a timeout to show an error message if authentication takes too long
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!isAuthenticated && !loading) {
        setAuthTimeout(true);
      }
    }, 5000); // 5 seconds timeout

    return () => clearTimeout(timer);
  }, [isAuthenticated, loading]);

  // Authentication is handled automatically in the AuthContext
  // This component just provides a loading state while that happens

  // If authenticated and not loading, redirect to home
  // The AuthContext will handle logging out and re-authenticating if needed
  if (isAuthenticated && !loading) {
    return <Navigate to="/" />;
  }

  if (!phoneNumber) {
    return <Navigate to="/auth-required" />;
  }

  // Format phone number for display
  const displayPhone = phoneNumber.startsWith('91')
    ? phoneNumber.substring(2)
    : phoneNumber;

  return (
    <div className="min-h-screen pt-20 flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-md p-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Authentication</h1>
          <p className="text-gray-600">Verifying your phone number</p>
        </div>

        <div className="flex items-center justify-center mb-6">
          <div className="bg-primary-100 rounded-full p-3">
            <Phone className="h-8 w-8 text-primary-600" />
          </div>
        </div>

        <div className="text-center mb-6">
          <p className="text-lg font-medium text-gray-800">+{displayPhone}</p>
        </div>

        {loading && (
          <div className="flex flex-col items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 mb-4"></div>
            <p className="text-gray-600">Authenticating...</p>
          </div>
        )}

        {authTimeout && !loading && (
          <div className="mt-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-sm text-yellow-800 mb-4">
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 text-yellow-600" />
                <div>
                  <p className="font-medium">Authentication is taking longer than expected</p>
                  <p className="mt-1">This could be because:</p>
                  <ul className="list-disc list-inside mt-1">
                    <li>The phone number is not registered in our system</li>
                    <li>There might be a connection issue with our database</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex justify-center">
              <Link
                to="/auth-required"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              >
                Go Back
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PhoneAuthPage;
