# Progress

**What Works:**

-   Basic project setup with React, TypeScript, Tailwind CSS, and Vite.
-   Database schema defined in `schema.md`.
-   Created core memory bank files.

**What's Left to Build:**

-   Implement the product catalog with categories and subcategories.
-   Implement the shopping cart functionality.
-   Implement user authentication and authorization.
-   Implement the checkout process with payment integration.
-   Implement customer profile management.
-   Implement the admin panel.

**Current Status:**

-   Project is in progress.
-   Memory bank initialized and updated.
-   Razorpay integration is in progress.
-   Address management is implemented.

**Known Issues:**

-   None at this time.
