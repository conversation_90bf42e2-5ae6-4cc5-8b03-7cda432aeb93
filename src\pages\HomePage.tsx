import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, ShoppingBag, TrendingUp } from 'lucide-react';
import CategoryCard from '../components/CategoryCard';
import ProductCard from '../components/ProductCard';
import { fetchCategories, fetchProducts } from '../lib/api';
import { Category, Product } from '../types';

const HomePage: React.FC = () => {
  const [query, setQuery] = useState('');
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>([]);
  const [mostPopular, setMostPopular] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Fetch categories
        const categoriesData = await fetchCategories();
        setCategories(categoriesData);

        // Fetch products for popular section
        const productsData = await fetchProducts();
        // Simulate getting most popular products by shuffling
        const shuffled = [...productsData].sort(() => 0.5 - Math.random());
        setMostPopular(shuffled.slice(0, 8));

        setError(null);
      } catch (err) {
        console.error('Error loading homepage data:', err);
        setError('Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      navigate(`/search?q=${encodeURIComponent(query)}`);
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="pt-24 bg-gradient-to-b from-green-50 to-white">
        <div className="container mx-auto px-4 py-12 md:py-24">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div className="order-2 md:order-1">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-800 leading-tight">
                Fresh Groceries Delivered to Your Doorstep
              </h1>
              <p className="mt-4 text-lg text-gray-600">
                Shop for fresh fruits, vegetables, and daily essentials from the comfort of your home.
              </p>

              <form onSubmit={handleSearch} className="mt-8 relative">
                <div className="flex">
                  <div className="relative w-full">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      value={query}
                      onChange={(e) => setQuery(e.target.value)}
                      placeholder="Search for fruits, vegetables, etc."
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                  <button
                    type="submit"
                    className="bg-green-600 hover:bg-green-700 text-white px-6 rounded-r-lg flex items-center transition-colors"
                  >
                    Search
                  </button>
                </div>
              </form>

              <div className="mt-8 flex items-center space-x-4">
                <span className="text-gray-700 font-medium">Popular:</span>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => navigate('/category/fruits')}
                    className="px-3 py-1 bg-white text-green-600 border border-green-200 rounded-full text-sm hover:bg-green-50 transition-colors"
                  >
                    Fruits
                  </button>
                  <button
                    onClick={() => navigate('/category/vegetables')}
                    className="px-3 py-1 bg-white text-green-600 border border-green-200 rounded-full text-sm hover:bg-green-50 transition-colors"
                  >
                    Vegetables
                  </button>
                  <button
                    onClick={() => navigate('/category/dairy')}
                    className="px-3 py-1 bg-white text-green-600 border border-green-200 rounded-full text-sm hover:bg-green-50 transition-colors"
                  >
                    Dairy
                  </button>
                </div>
              </div>
            </div>

            <div className="order-1 md:order-2 flex justify-center">
              <img
                src="https://images.pexels.com/photos/1132047/pexels-photo-1132047.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Fresh Groceries"
                className="rounded-lg shadow-lg max-h-96 object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-800">Shop by Category</h2>
            <button
              onClick={() => navigate('/categories')}
              className="text-green-600 hover:text-green-700 font-medium flex items-center space-x-1"
            >
              <span>View All</span>
            </button>
          </div>

          {loading && (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
            </div>
          )}

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {!loading && !error && categories.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No categories found</p>
            </div>
          )}

          {!loading && !error && categories.length > 0 && (
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
              {categories.slice(0, 4).map((category) => (
                <CategoryCard key={category.id} category={category} />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Shop All Categories Button */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="mt-8 text-center">
            <button
              onClick={() => navigate('/categories')}
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors"
            >
              <ShoppingBag className="mr-2 h-5 w-5" />
              Shop All Categories
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-green-50 p-6 rounded-lg text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Fresh Quality</h3>
              <p className="text-gray-600 text-sm">We ensure that all our products meet the highest quality standards and freshness.</p>
            </div>

            <div className="bg-green-50 p-6 rounded-lg text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Quick Delivery</h3>
              <p className="text-gray-600 text-sm">Get your groceries delivered to your doorstep within hours of placing an order.</p>
            </div>

            <div className="bg-green-50 p-6 rounded-lg text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Easy Payments</h3>
              <p className="text-gray-600 text-sm">Multiple secure payment options including UPI, cards, and cash on delivery.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;