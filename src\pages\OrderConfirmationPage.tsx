import React, { useEffect, useState } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { ChevronRight, CheckCircle, ShoppingBag, MapPin, CreditCard, TruckIcon } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../context/AuthContext';

interface OrderItem {
  id: string;
  order_id: string;
  product_name: string;
  product_description: string;
  product_image: string;
  quantity: number;
  price: number;
}

interface Address {
  id: string;
  customer_id: string;
  address: string;
  pincode: string;
  is_default: boolean;
}

interface Order {
  id: string;
  customer_id: string;
  address_id: string;
  payment_method: string;
  payment_status: string;
  status: string;
  total_amount: number;
  created_at: string;
  razorpay_payment_id?: string;
  addresses?: Address;
  address?: Address;
  items?: OrderItem[];
}

const OrderConfirmationPage: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, customer, loading } = useAuth();
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated && !loading) {
      navigate('/');
      return;
    }

    if (!orderId) {
      setError('Order ID is missing');
      setIsLoading(false);
      return;
    }

    const fetchOrderDetails = async () => {
      try {
        // Fetch order details with address information
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select(`
            *,
            addresses:address_id (*)
          `)
          .eq('id', orderId)
          .single();

        if (orderError) {
          throw new Error(orderError.message);
        }

        if (!orderData) {
          throw new Error('Order not found');
        }

        // Check if this order belongs to the current customer
        if (orderData.customer_id !== customer?.id) {
          throw new Error('You do not have permission to view this order');
        }

        // Fetch order items
        const { data: orderItems, error: itemsError } = await supabase
          .from('order_items')
          .select('*')
          .eq('order_id', orderId);

        if (itemsError) {
          throw new Error(itemsError.message);
        }

        // Log the data for debugging
        console.log('Order data:', orderData);
        console.log('Address data:', orderData.addresses);

        // Combine order with its items
        const completeOrder: Order = {
          ...orderData,
          address: orderData.addresses,
          items: orderItems || []
        };

        setOrder(completeOrder);
      } catch (err: any) {
        console.error('Error fetching order details:', err);
        setError(err.message || 'Failed to load order details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderDetails();
  }, [orderId, isAuthenticated, customer, loading, navigate]);

  if (loading || isLoading) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="text-center p-6 bg-white rounded-lg shadow-md max-w-md">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link to="/" className="inline-block px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
            Return to Home
          </Link>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="text-center">
          <ShoppingBag className="mx-auto h-12 w-12 text-gray-400" />
          <h2 className="mt-2 text-lg font-medium text-gray-900">Order not found</h2>
          <p className="mt-1 text-sm text-gray-500">We couldn't find the order you're looking for</p>
          <div className="mt-6">
            <Link
              to="/"
              className="inline-flex justify-center px-4 py-2 border border-transparent
              rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
            >
              Return to Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Format date
  const orderDate = new Date(order.created_at).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  return (
    <div className="min-h-screen pt-20">
      <div className="bg-primary-50 py-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600 mb-2">
            <Link to="/" className="hover:text-primary-600">Home</Link>
            <ChevronRight className="w-4 h-4 mx-1" />
            <span className="text-gray-800">Order Confirmation</span>
          </div>

          <h1 className="text-3xl font-bold text-gray-800">Order Confirmation</h1>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-500 mr-3" />
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Thank You for Your Order!</h2>
                <p className="text-gray-600">Your order has been placed successfully.</p>
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Order Information</h3>
                <div className="bg-gray-50 p-4 rounded-md">
                  <p className="text-gray-700"><span className="font-medium">Order ID:</span> {order.id}</p>
                  <p className="text-gray-700"><span className="font-medium">Date:</span> {orderDate}</p>
                  <p className="text-gray-700"><span className="font-medium">Status:</span> {order.status}</p>
                  <p className="text-gray-700"><span className="font-medium">Total Amount:</span> ₹{order.total_amount}</p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Payment Information</h3>
                <div className="bg-gray-50 p-4 rounded-md">
                  <p className="text-gray-700"><span className="font-medium">Payment Method:</span> {order.payment_method}</p>
                  <p className="text-gray-700"><span className="font-medium">Payment Status:</span> {order.payment_status}</p>
                  {order.razorpay_payment_id && (
                    <p className="text-gray-700"><span className="font-medium">Payment ID:</span> {order.razorpay_payment_id}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-800 mb-2">Delivery Address</h3>
              <div className="bg-gray-50 p-4 rounded-md flex items-start">
                <MapPin className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                <div>
                  {(order.address || order.addresses) ? (
                    <>
                      <p className="text-gray-700">{order.address?.address || order.addresses?.address || 'No address available'}</p>
                      {(order.address?.pincode || order.addresses?.pincode) && (
                        <p className="text-gray-700 mt-1">
                          <span className="font-medium">Pincode:</span> {order.address?.pincode || order.addresses?.pincode}
                        </p>
                      )}
                    </>
                  ) : (
                    <p className="text-gray-700">Address information not available</p>
                  )}
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Order Items</h3>
              <div className="border rounded-md overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {order.items?.map((item) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-10 w-10 flex-shrink-0">
                              <img className="h-10 w-10 rounded-md object-cover" src={item.product_image} alt={item.product_name} />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{item.product_name}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">₹{item.price}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{item.quantity}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">₹{item.price * item.quantity}</div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div className="p-6 bg-gray-50 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <div className="flex items-center text-primary-600">
                <TruckIcon className="h-5 w-5 mr-2" />
                <span>Your order will be delivered within 24-48 hours</span>
              </div>
              <Link
                to="/"
                className="inline-flex justify-center px-4 py-2 border border-transparent
                rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
              >
                Continue Shopping
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmationPage;
