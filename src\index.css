@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideDown {
  animation: slideDown 0.3s ease-out;
}

/* Custom styles */
body {
  @apply bg-gray-50 text-gray-800 font-sans;
}

/* Add the default focus style to form elements and specific buttons */
input:focus,
select:focus,
textarea:focus,
button:focus:not(.no-focus-ring) {
  @apply outline-none ring-2 ring-primary-500 ring-opacity-50;
}

/* Remove focus styles for navigation elements */
a:focus,
.logo-link:focus,
.nav-button:focus {
  @apply outline-none;
}

/* Line clamp utility for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full hover:bg-gray-400;
}