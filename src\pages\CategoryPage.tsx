import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';
import SubcategoryCard from '../components/SubcategoryCard';
import { fetchCategories, fetchSubcategories } from '../lib/api';
import { Category, Subcategory } from '../types';

const CategoryPage: React.FC = () => {
  const { categoryId } = useParams<{ categoryId: string }>();
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const category = categories.find(cat => cat.id === categoryId);

  // Fetch categories and subcategories
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Fetch categories
        const categoriesData = await fetchCategories();
        setCategories(categoriesData);

        // Fetch subcategories for this category
        if (categoryId) {
          const subcategoriesData = await fetchSubcategories(categoryId);
          setSubcategories(subcategoriesData);
        }

        setError(null);
      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [categoryId]);

  if (!category) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-800">Category not found</h2>
          <p className="mt-2 text-gray-600">The category you're looking for doesn't exist.</p>
          <Link to="/categories" className="mt-4 inline-block text-primary-600 hover:text-primary-700">
            Browse all categories
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-16">
      <div
        className="bg-primary-50 bg-opacity-80 py-8"
        style={{
          backgroundImage: `url(${category.image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundBlendMode: 'overlay'
        }}
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600 mb-2">
            <Link to="/" className="hover:text-primary-600">Home</Link>
            <ChevronRight className="w-4 h-4 mx-1" />
            <Link to="/categories" className="hover:text-primary-600">Categories</Link>
            <ChevronRight className="w-4 h-4 mx-1" />
            <span className="text-gray-800">{category.name}</span>
          </div>

          <h1 className="text-3xl font-bold text-gray-800">{category.name}</h1>
          <p className="text-gray-600 mt-2">{category.description}</p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">Subcategories</h2>

        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {!loading && !error && subcategories.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No subcategories found in this category</p>
            <Link to="/categories" className="mt-4 inline-block text-primary-600 hover:text-primary-700">
              Browse other categories
            </Link>
          </div>
        ) : !loading && !error ? (
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
            {subcategories.map((subcategory) => (
              <SubcategoryCard key={subcategory.id} subcategory={subcategory} />
            ))}
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default CategoryPage;