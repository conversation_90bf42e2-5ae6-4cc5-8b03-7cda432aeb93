import React from 'react';
import { Phone, AlertCircle } from 'lucide-react';

const AuthRequiredPage: React.FC = () => {
  return (
    <div className="min-h-screen pt-20 flex items-center justify-center bg-gray-50">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-primary-600" />
          <h1 className="mt-4 text-2xl font-bold text-gray-900">Authentication Required</h1>
          <p className="mt-2 text-gray-600">
            This application requires authentication via WhatsApp.
          </p>
        </div>

        <div className="bg-primary-50 border border-primary-200 rounded-lg p-4 text-sm text-primary-800">
          <p className="flex items-start">
            <Phone className="h-5 w-5 mr-2 flex-shrink-0 text-primary-600" />
            <span>
              Please use the link sent to your WhatsApp to access this application.
              The link should include your phone number for authentication.
            </span>
          </p>
        </div>

        <div className="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4 text-sm text-gray-700">
          <h3 className="font-medium mb-2">How Authentication Works:</h3>
          <ul className="list-disc list-inside space-y-1">
            <li>Each WhatsApp link contains your phone number for authentication</li>
            <li>When you click a new link, you'll be automatically logged in with that phone number</li>
            <li>If you were already logged in with a different number, your session will switch automatically</li>
            <li>Your session remains active until you log out or use a different link</li>
          </ul>
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            If you haven't received a link, please contact our customer support.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthRequiredPage;
