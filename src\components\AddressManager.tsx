import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Plus, Check, Home, Star, Trash2, MoreVertical } from 'lucide-react';
import { useAuth } from '../context/AuthContext';

interface AddressManagerProps {
  onSelectAddress?: (addressId: string) => void;
  selectedAddressId?: string;
}

const AddressManager: React.FC<AddressManagerProps> = ({
  onSelectAddress,
  selectedAddressId
}) => {
  const { addresses, addAddress, setDefaultAddress, deleteAddress, loading } = useAuth();
  const [showAddForm, setShowAddForm] = useState(false);
  const [newAddress, setNewAddress] = useState('');
  const [newPincode, setNewPincode] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showDropdown, setShowDropdown] = useState<string | null>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      // Don't close if clicking on dropdown content
      if (!target.closest('.dropdown-menu')) {
        setShowDropdown(null);
      }
    };

    if (showDropdown) {
      // Use a small delay to prevent immediate closing
      const timer = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
      }, 100);

      return () => {
        clearTimeout(timer);
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showDropdown]);

  // Auto-dismiss success/error messages
  useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess(null);
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success, error]);

  const handleAddAddress = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newAddress.trim()) {
      setError('Please enter an address');
      return;
    }

    if (!newPincode.trim()) {
      setError('Please enter a pincode');
      return;
    }

    try {
      setError(null);
      const result = await addAddress(newAddress, newPincode);

      if (result.success) {
        setSuccess('Address added successfully');
        setNewAddress('');
        setNewPincode('');
        setShowAddForm(false);
      } else {
        setError(result.message);
      }
    } catch (err) {
      console.error('Error adding address:', err);
      setError('Failed to add address. Please try again.');
    }
  };

  const handleSelectAddress = (addressId: string) => {
    if (onSelectAddress) {
      onSelectAddress(addressId);
    }
  };

  const handleSetDefault = async (addressId: string) => {
    try {
      setError(null);
      const result = await setDefaultAddress(addressId);

      if (result.success) {
        setSuccess(result.message);
        setShowDropdown(null);
      } else {
        setError(result.message);
      }
    } catch (err) {
      console.error('Error setting default address:', err);
      setError('Failed to set default address. Please try again.');
    }
  };

  const handleDeleteAddress = async (addressId: string) => {
    if (addresses.length === 1) {
      setError('Cannot delete the only address. Add another address first.');
      return;
    }

    if (window.confirm('Are you sure you want to delete this address?')) {
      try {
        setError(null);
        const result = await deleteAddress(addressId);

        if (result.success) {
          setSuccess(result.message);
          setShowDropdown(null);
        } else {
          setError(result.message);
        }
      } catch (err) {
        console.error('Error deleting address:', err);
        setError('Failed to delete address. Please try again.');
      }
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-800">Delivery Address</h2>
        {addresses.length > 0 && (
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="flex items-center text-primary-600 hover:text-primary-700"
          >
            <Plus className="w-4 h-4 mr-1" />
            <span>Add New</span>
          </button>
        )}
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {/* Address List */}
      {addresses.length > 0 ? (
        <div className="space-y-3 mb-4">
          {addresses.map((address) => (
            <div
              key={address.id}
              className={`border rounded-lg p-3 transition-colors ${
                selectedAddressId === address.id
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-200 hover:border-primary-300'
              }`}
            >
              <div className="flex items-start">
                <div
                  className="flex-1 cursor-pointer"
                  onClick={() => handleSelectAddress(address.id)}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      {address.is_default ? (
                        <Home className="w-5 h-5 text-primary-600" />
                      ) : (
                        <MapPin className="w-5 h-5 text-gray-400" />
                      )}
                    </div>
                    <div className="ml-3 flex-1">
                      <p className="text-gray-700">{address.address}</p>
                      {address.pincode && (
                        <p className="text-sm text-gray-500 mt-1">Pincode: {address.pincode}</p>
                      )}
                      {address.is_default && (
                        <span className="text-xs text-primary-600 font-medium">Default Address</span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {selectedAddressId === address.id && (
                    <div className="flex-shrink-0">
                      <Check className="w-5 h-5 text-primary-600" />
                    </div>
                  )}

                  {/* Dropdown Menu */}
                  <div className="relative">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowDropdown(showDropdown === address.id ? null : address.id);
                      }}
                      className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <MoreVertical className="w-4 h-4" />
                    </button>

                    {showDropdown === address.id && (
                      <div
                        className="dropdown-menu absolute right-0 top-8 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[160px]"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {!address.is_default && (
                          <button
                            onClick={() => handleSetDefault(address.id)}
                            className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center"
                          >
                            <Star className="w-4 h-4 mr-2" />
                            Set as Default
                          </button>
                        )}
                        {addresses.length > 1 && (
                          <button
                            onClick={() => handleDeleteAddress(address.id)}
                            className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-6 border border-dashed border-gray-300 rounded-lg mb-4">
          <MapPin className="w-10 h-10 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500 mb-2">No addresses found</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
          >
            Add New Address
          </button>
        </div>
      )}

      {/* Add Address Form */}
      {showAddForm && (
        <form onSubmit={handleAddAddress} className="border border-gray-200 rounded-lg p-4">
          <h3 className="text-md font-medium text-gray-800 mb-3">Add New Address</h3>

          <div className="mb-4">
            <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
              Address
            </label>
            <textarea
              id="address"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter your full address including house/flat number, street, area, city, state"
              value={newAddress}
              onChange={(e) => setNewAddress(e.target.value)}
              required
            />
            <p className="mt-1 text-sm text-gray-500">
              Example: 123, Main Street, Anna Nagar, Chennai, Tamil Nadu
            </p>
          </div>

          <div className="mb-4">
            <label htmlFor="pincode" className="block text-sm font-medium text-gray-700 mb-1">
              Pincode
            </label>
            <input
              type="text"
              id="pincode"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter 6-digit pincode"
              value={newPincode}
              onChange={(e) => setNewPincode(e.target.value)}
              maxLength={6}
              pattern="[0-9]{6}"
              required
            />
            <p className="mt-1 text-sm text-gray-500">
              Example: 600040
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => {
                setShowAddForm(false);
                setNewAddress('');
                setNewPincode('');
                setError(null);
              }}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className={`px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 ${
                loading ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              {loading ? 'Adding...' : 'Save Address'}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default AddressManager;
