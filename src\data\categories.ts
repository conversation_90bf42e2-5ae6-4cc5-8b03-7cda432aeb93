import { Category } from '../types';

export const categories: Category[] = [
  {
    id: 'fruits',
    name: 'Fruits',
    image: 'https://images.pexels.com/photos/1132047/pexels-photo-1132047.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Fresh and seasonal fruits from local farms'
  },
  {
    id: 'vegetables',
    name: 'Vegetables',
    image: 'https://images.pexels.com/photos/2733918/pexels-photo-2733918.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Organic and locally sourced vegetables'
  },
  {
    id: 'dairy',
    name: 'Dairy & Eggs',
    image: 'https://images.pexels.com/photos/248412/pexels-photo-248412.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Fresh milk, curd, paneer, and eggs'
  },
  {
    id: 'spices',
    name: 'Spices',
    image: 'https://images.pexels.com/photos/2802527/pexels-photo-2802527.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Authentic Indian spices and masalas'
  },
  {
    id: 'grains',
    name: 'Rice & Grains',
    image: 'https://images.pexels.com/photos/4110251/pexels-photo-4110251.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Premium quality rice, dals, and whole grains'
  },
  {
    id: 'snacks',
    name: 'Snacks',
    image: 'https://images.pexels.com/photos/1638280/pexels-photo-1638280.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Tasty Indian snacks and namkeens'
  },
  {
    id: 'beverages',
    name: 'Beverages',
    image: 'https://images.pexels.com/photos/1292862/pexels-photo-1292862.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Tea, coffee, juices and more'
  },
  {
    id: 'household',
    name: 'Household',
    image: 'https://images.pexels.com/photos/4239013/pexels-photo-4239013.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Cleaning supplies and household essentials'
  }
];