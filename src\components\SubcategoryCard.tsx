import React from 'react';
import { Link } from 'react-router-dom';
import { Subcategory } from '../types';

interface SubcategoryCardProps {
  subcategory: Subcategory;
}

const SubcategoryCard: React.FC<SubcategoryCardProps> = ({ subcategory }) => {
  return (
    <Link
      to={`/subcategory/${subcategory.id}`}
      className="block group overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300 bg-white"
    >
      <div className="relative h-40 overflow-hidden">
        <img
          src={subcategory.image_url}
          alt={subcategory.name}
          className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        <h3 className="absolute bottom-4 left-4 text-white text-lg font-semibold">{subcategory.name}</h3>
      </div>
      <div className="p-3">
        <div className="mt-2 flex justify-between items-center">
          <span className="text-primary-600 font-medium text-sm">Browse Products</span>
        </div>
      </div>
    </Link>
  );
};

export default SubcategoryCard;
