import { supabase } from './supabase';
import { Order } from '../types';

// Get Razorpay key from environment variables
const RAZORPAY_KEY_ID = import.meta.env.VITE_RAZORPAY_KEY_ID || 'rzp_test_szPCDagUzGKLlH';

// Initialize Razorpay
export const initRazorpay = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.onload = () => resolve(true);
    script.onerror = () => resolve(false);
    document.body.appendChild(script);
  });
};

// Create a Razorpay order
export const createRazorpayOrder = async (
  orderId: string,
  amount: number,
  currency: string = 'INR'
): Promise<{ id: string } | null> => {
  try {
    console.log('Creating Razorpay order for order ID:', orderId, 'amount:', amount);

    // For a proper implementation, this should be done server-side
    // Since we don't have a backend, we'll skip the order creation step
    // and use a different approach for the checkout

    // Instead of creating an order, we'll use the "prefill" option in Razorpay
    // This allows us to skip the order creation step for testing purposes

    // Return a special value that indicates we're using the prefill approach
    return {
      id: "skip_order_creation"
    };
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    return null;
  }
};

// Open Razorpay payment modal
export const openRazorpayCheckout = (
  orderDetails: {
    id: string;
    amount: number;
    currency: string;
    razorpayOrderId: string;
  },
  customerDetails: {
    name: string;
    email: string;
    phone: string;
  },
  onSuccess: (paymentId: string, orderId: string, signature: string) => void,
  onFailure: (error: any) => void
) => {
  // Base options for Razorpay
  const options: any = {
    key: RAZORPAY_KEY_ID,
    amount: orderDetails.amount * 100, // Razorpay expects amount in paise
    currency: orderDetails.currency,
    name: 'Dhanam Supermarket',
    description: `Order #${orderDetails.id}`,
    prefill: {
      name: customerDetails.name,
      email: customerDetails.email,
      contact: customerDetails.phone,
    },
    notes: {
      order_id: orderDetails.id // This is important for the webhook to identify the order
    },
    theme: {
      color: '#3B82F6', // Primary blue color
    },
    modal: {
      confirm_close: true,
      ondismiss: function() {
        console.log('Checkout form closed');
        onFailure({ error: { description: 'Payment cancelled by user' } });
      }
    },
    handler: function (response: any) {
      console.log('Payment successful, response:', response);
      // Handle successful payment
      onSuccess(
        response.razorpay_payment_id || 'test_payment_id',
        response.razorpay_order_id || orderDetails.id,
        response.razorpay_signature || 'test_signature'
      );
    },
  };

  // Only add order_id if we have a valid Razorpay order ID
  // For our test implementation, we're skipping this
  if (orderDetails.razorpayOrderId && orderDetails.razorpayOrderId !== 'skip_order_creation') {
    options.order_id = orderDetails.razorpayOrderId;
  }

  const razorpayWindow = new (window as any).Razorpay(options);
  razorpayWindow.on('payment.failed', onFailure);
  razorpayWindow.open();
};

// Update order with Razorpay payment details
export const updateOrderWithPayment = async (
  orderId: string,
  paymentId: string,
  paymentStatus: 'completed' | 'failed'
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('orders')
      .update({
        razorpay_payment_id: paymentId,
        payment_status: paymentStatus === 'completed' ? 'paid' : 'failed',
        payment_auth_date: new Date().toISOString(),
        ...(paymentStatus === 'completed'
          ? { payment_capture_date: new Date().toISOString() }
          : { payment_failure_date: new Date().toISOString() })
      })
      .eq('id', orderId);

    if (error) {
      console.error('Error updating order with payment details:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error updating order with payment details:', error);
    return false;
  }
};

// Verify Razorpay payment (in a real implementation, this would be done server-side)
export const verifyRazorpayPayment = async (
  paymentId: string,
  orderId: string,
  signature: string
): Promise<boolean> => {
  try {
    console.log('Verifying payment:', { paymentId, orderId, signature });

    // In a real implementation, this would be a server-side API call to verify the signature
    // For our test implementation, we'll always return true

    // For test payments, we might not have a signature
    if (!signature && paymentId) {
      console.log('Test payment detected - skipping verification');
      return true;
    }

    // For real payments with a signature, we would verify it server-side
    // But for our test implementation, we'll just simulate a successful verification
    console.log('Payment verification successful');
    return true;

    // In a real implementation with a backend API:
    // const response = await fetch('/api/verify-razorpay-payment', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ paymentId, orderId, signature })
    // });
    // const result = await response.json();
    // return result.verified;
  } catch (error) {
    console.error('Error verifying Razorpay payment:', error);
    return false;
  }
};

