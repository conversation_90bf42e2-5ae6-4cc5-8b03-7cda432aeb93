import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Phone, AlertCircle, Check, X } from 'lucide-react';
import { checkCustomerExists, createTestCustomer } from '../utils/testAuth';

const TestAuthPage: React.FC = () => {
  const { phoneNumber } = useParams<{ phoneNumber: string }>();
  const [loading, setLoading] = useState<boolean>(true);
  const [exists, setExists] = useState<boolean | null>(null);
  const [created, setCreated] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const checkCustomer = async () => {
      if (!phoneNumber) {
        setError('No phone number provided');
        setLoading(false);
        return;
      }
      
      try {
        // Check if customer exists
        const customerExists = await checkCustomerExists(phoneNumber);
        setExists(customerExists);
        
        if (!customerExists) {
          // Create a test customer
          const customerCreated = await createTestCustomer(phoneNumber);
          setCreated(customerCreated);
        }
      } catch (err) {
        console.error('Error in test auth:', err);
        setError('An error occurred during testing');
      } finally {
        setLoading(false);
      }
    };
    
    checkCustomer();
  }, [phoneNumber]);
  
  // Format phone number for display
  const displayPhone = phoneNumber ? 
    (phoneNumber.startsWith('91') ? phoneNumber.substring(2) : phoneNumber) 
    : 'Unknown';
  
  return (
    <div className="min-h-screen pt-16 flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-md p-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Authentication Test</h1>
          <p className="text-gray-600">Testing phone number: +{displayPhone}</p>
        </div>
        
        <div className="flex items-center justify-center mb-6">
          <div className="bg-primary-100 rounded-full p-3">
            <Phone className="h-8 w-8 text-primary-600" />
          </div>
        </div>
        
        {loading && (
          <div className="flex flex-col items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 mb-4"></div>
            <p className="text-gray-600">Testing authentication...</p>
          </div>
        )}
        
        {!loading && (
          <div className="space-y-4">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-sm text-red-800">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 text-red-600" />
                  <span>{error}</span>
                </div>
              </div>
            )}
            
            {exists !== null && (
              <div className={`border rounded-lg p-4 ${exists ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'}`}>
                <div className="flex items-start">
                  {exists ? (
                    <Check className="h-5 w-5 mr-2 flex-shrink-0 text-green-600" />
                  ) : (
                    <X className="h-5 w-5 mr-2 flex-shrink-0 text-yellow-600" />
                  )}
                  <span className={exists ? 'text-green-800' : 'text-yellow-800'}>
                    Customer {exists ? 'exists' : 'does not exist'} with phone number +{displayPhone}
                  </span>
                </div>
              </div>
            )}
            
            {created !== null && (
              <div className={`border rounded-lg p-4 ${created ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                <div className="flex items-start">
                  {created ? (
                    <Check className="h-5 w-5 mr-2 flex-shrink-0 text-green-600" />
                  ) : (
                    <X className="h-5 w-5 mr-2 flex-shrink-0 text-red-600" />
                  )}
                  <span className={created ? 'text-green-800' : 'text-red-800'}>
                    Test customer {created ? 'created successfully' : 'creation failed'}
                  </span>
                </div>
              </div>
            )}
            
            <div className="mt-6 flex justify-center space-x-4">
              <Link 
                to="/auth-required" 
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              >
                Go Back
              </Link>
              
              {(exists || created) && (
                <Link 
                  to={`/auth/${phoneNumber}`} 
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
                >
                  Try Authentication
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestAuthPage;
