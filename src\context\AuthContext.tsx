import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../lib/supabase';
import { useLocation, useNavigate } from 'react-router-dom';

interface Customer {
  id: string;
  name: string;
  whatsapp_number: string;
  email: string | null;
  created_at: string;
}

interface Address {
  id: string;
  customer_id: string;
  address: string;
  pincode: string;
  is_default: boolean;
}

interface AuthContextType {
  isAuthenticated: boolean;
  customer: Customer | null;
  addresses: Address[];
  logout: () => void;
  addAddress: (address: string, pincode: string) => Promise<{ success: boolean; message: string }>;
  updateAddress: (addressId: string, address: string, pincode: string) => Promise<{ success: boolean; message: string }>;
  setDefaultAddress: (addressId: string) => Promise<{ success: boolean; message: string }>;
  deleteAddress: (addressId: string) => Promise<{ success: boolean; message: string }>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();
  const location = useLocation();

  // Extract phone number from URL path
  const getPhoneNumberFromUrl = () => {
    // Only look for phone numbers in auth URLs
    if (!location.pathname.includes('/auth/')) {
      return null;
    }

    const pathParts = location.pathname.split('/');
    // Check if the last part of the URL is a phone number
    const lastPart = pathParts[pathParts.length - 1];

    // Handle phone number with or without country code
    let phoneNumber = lastPart;

    // If it starts with country code (91), remove it
    if (phoneNumber.startsWith('91') && phoneNumber.length === 12) {
      phoneNumber = phoneNumber.substring(2);
    }

    // Simple validation for Indian phone number (10 digits)
    const phoneRegex = /^[6-9]\d{9}$/;
    if (phoneRegex.test(phoneNumber)) {
      console.log('Valid phone number found:', phoneNumber);
      return phoneNumber;
    }

    console.log('Invalid phone number format or not in auth URL:', lastPart);
    return null;
  };

  // Check authentication on mount and when URL changes
  useEffect(() => {
    const checkAuth = async () => {
      try {
        setLoading(true);
        console.log('Checking authentication...');

        // Check for phone number in URL first
        const phoneNumber = getPhoneNumberFromUrl();
        console.log('Extracted phone number from URL:', phoneNumber);

        // If we have a phone number in the URL, always try to authenticate with it
        // This will override any existing session
        if (phoneNumber) {
          console.log('Phone number found in URL, attempting to authenticate with it');

          // Clear any existing session
          localStorage.removeItem('customer');
          setCustomer(null);
          setIsAuthenticated(false);
          setAddresses([]);

          // Continue with authentication using the phone number from URL
        }
        // If no phone number in URL, check for stored customer
        else {
          const storedCustomer = localStorage.getItem('customer');
          if (storedCustomer) {
            console.log('Found stored customer');
            const parsedCustomer = JSON.parse(storedCustomer);
            setCustomer(parsedCustomer);
            setIsAuthenticated(true);
            await fetchAddresses(parsedCustomer.id);
            setLoading(false);
            return;
          }
        }

        if (phoneNumber) {
          console.log('Checking if phone number exists in database:', phoneNumber);

          // Try different formats of the phone number
          const formats = [
            phoneNumber,                    // Original format
            phoneNumber.replace(/^91/, ''), // Without country code if it starts with 91
            `91${phoneNumber}`,             // With country code if it doesn't have one
          ];

          console.log('Trying phone number formats:', formats);

          let customerData = null;
          let queryError = null;

          // Try each format
          for (const format of formats) {
            console.log(`Checking format: ${format}`);
            const { data, error } = await supabase
              .from('customers')
              .select('*')
              .eq('whatsapp_number', format);

            console.log(`Result for format ${format}:`, { data, error });

            if (!error && data && data.length > 0) {
              console.log('Customer found with format:', format);
              customerData = data[0];
              break;
            }

            queryError = error;
          }

          console.log('Final result:', { customerData, queryError });

          if (customerData) {
            console.log('Customer found:', customerData);

            // Store customer data
            setCustomer(customerData);
            setIsAuthenticated(true);
            localStorage.setItem('customer', JSON.stringify(customerData));

            // Fetch addresses
            await fetchAddresses(customerData.id);

            // Navigate to home page
            console.log('Authentication successful, redirecting to home');
            navigate('/');
          } else {
            console.log('Phone number not found in database or error occurred');
            setLoading(false);
          }
        } else {
          console.log('No valid phone number found in URL');
          setLoading(false);
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        localStorage.removeItem('customer');
        setLoading(false);
      }
    };

    checkAuth();
  }, [location.pathname, navigate]);

  const fetchAddresses = async (customerId: string) => {
    try {
      const { data, error } = await supabase
        .from('addresses')
        .select('*')
        .eq('customer_id', customerId);

      if (error) {
        console.error('Error fetching addresses:', error);
        return;
      }

      setAddresses(data || []);
    } catch (error) {
      console.error('Error fetching addresses:', error);
    }
  };

  const logout = () => {
    setCustomer(null);
    setIsAuthenticated(false);
    setAddresses([]);
    localStorage.removeItem('customer');
    navigate('/');
  };

  const addAddress = async (address: string, pincode: string): Promise<{ success: boolean; message: string }> => {
    if (!customer) {
      return { success: false, message: 'You must be logged in to add an address' };
    }

    try {
      setLoading(true);

      // Validate inputs
      if (!address.trim()) {
        return { success: false, message: 'Please enter a valid address' };
      }

      if (!pincode.trim()) {
        return { success: false, message: 'Please enter a pincode' };
      }

      // Simple validation for Indian pincode (6 digits)
      const pincodeRegex = /^\d{6}$/;
      if (!pincodeRegex.test(pincode.trim())) {
        return { success: false, message: 'Please enter a valid 6-digit pincode' };
      }

      // Check if this is the first address (should be default)
      const isDefault = addresses.length === 0;

      const { data, error } = await supabase
        .from('addresses')
        .insert([
          {
            customer_id: customer.id,
            address: address.trim(),
            pincode: pincode.trim(),
            is_default: isDefault
          }
        ])
        .select();

      if (error) {
        console.error('Error adding address:', error);
        return {
          success: false,
          message: 'Failed to add address. Please try again.'
        };
      }

      // Update addresses state
      if (data && data.length > 0) {
        setAddresses([...addresses, data[0]]);
      }

      return { success: true, message: 'Address added successfully' };
    } catch (error) {
      console.error('Error adding address:', error);
      return {
        success: false,
        message: 'An error occurred while adding the address. Please try again.'
      };
    } finally {
      setLoading(false);
    }
  };

  const updateAddress = async (addressId: string, address: string, pincode: string): Promise<{ success: boolean; message: string }> => {
    if (!customer) {
      return { success: false, message: 'You must be logged in to update an address' };
    }

    try {
      setLoading(true);

      // Validate inputs
      if (!address.trim()) {
        return { success: false, message: 'Please enter a valid address' };
      }

      if (!pincode.trim()) {
        return { success: false, message: 'Please enter a pincode' };
      }

      // Simple validation for Indian pincode (6 digits)
      const pincodeRegex = /^\d{6}$/;
      if (!pincodeRegex.test(pincode.trim())) {
        return { success: false, message: 'Please enter a valid 6-digit pincode' };
      }

      const { data, error } = await supabase
        .from('addresses')
        .update({
          address: address.trim(),
          pincode: pincode.trim()
        })
        .eq('id', addressId)
        .eq('customer_id', customer.id)
        .select();

      if (error) {
        console.error('Error updating address:', error);
        return {
          success: false,
          message: 'Failed to update address. Please try again.'
        };
      }

      // Update addresses state
      if (data && data.length > 0) {
        const updatedAddresses = addresses.map(addr =>
          addr.id === addressId ? data[0] : addr
        );
        setAddresses(updatedAddresses);
      }

      return { success: true, message: 'Address updated successfully' };
    } catch (error) {
      console.error('Error updating address:', error);
      return {
        success: false,
        message: 'An error occurred while updating the address. Please try again.'
      };
    } finally {
      setLoading(false);
    }
  };

  const setDefaultAddress = async (addressId: string): Promise<{ success: boolean; message: string }> => {
    if (!customer) {
      return { success: false, message: 'You must be logged in to set default address' };
    }

    try {
      setLoading(true);

      // First, set all addresses to non-default
      const { error: resetError } = await supabase
        .from('addresses')
        .update({ is_default: false })
        .eq('customer_id', customer.id);

      if (resetError) {
        console.error('Error resetting default addresses:', resetError);
        return {
          success: false,
          message: 'Failed to update default address. Please try again.'
        };
      }

      // Then, set the selected address as default
      const { error: setError } = await supabase
        .from('addresses')
        .update({ is_default: true })
        .eq('id', addressId)
        .eq('customer_id', customer.id);

      if (setError) {
        console.error('Error setting default address:', setError);
        return {
          success: false,
          message: 'Failed to set default address. Please try again.'
        };
      }

      // Update local state
      const updatedAddresses = addresses.map(addr => ({
        ...addr,
        is_default: addr.id === addressId
      }));
      setAddresses(updatedAddresses);

      return { success: true, message: 'Default address updated successfully' };
    } catch (error) {
      console.error('Error setting default address:', error);
      return {
        success: false,
        message: 'An error occurred while setting default address. Please try again.'
      };
    } finally {
      setLoading(false);
    }
  };

  const deleteAddress = async (addressId: string): Promise<{ success: boolean; message: string }> => {
    if (!customer) {
      return { success: false, message: 'You must be logged in to delete an address' };
    }

    try {
      setLoading(true);

      // Check if this is the default address
      const addressToDelete = addresses.find(addr => addr.id === addressId);
      const isDefaultAddress = addressToDelete?.is_default;

      const { error } = await supabase
        .from('addresses')
        .delete()
        .eq('id', addressId)
        .eq('customer_id', customer.id);

      if (error) {
        console.error('Error deleting address:', error);
        return {
          success: false,
          message: 'Failed to delete address. Please try again.'
        };
      }

      // Update local state
      const updatedAddresses = addresses.filter(addr => addr.id !== addressId);

      // If we deleted the default address and there are other addresses, make the first one default
      if (isDefaultAddress && updatedAddresses.length > 0) {
        const newDefaultId = updatedAddresses[0].id;
        await setDefaultAddress(newDefaultId);
      } else {
        setAddresses(updatedAddresses);
      }

      return { success: true, message: 'Address deleted successfully' };
    } catch (error) {
      console.error('Error deleting address:', error);
      return {
        success: false,
        message: 'An error occurred while deleting the address. Please try again.'
      };
    } finally {
      setLoading(false);
    }
  };

  const value = {
    isAuthenticated,
    customer,
    addresses,
    logout,
    addAddress,
    updateAddress,
    setDefaultAddress,
    deleteAddress,
    loading
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
