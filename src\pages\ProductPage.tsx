import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { ChevronRight, Minus, Plus, ShoppingCart, Share, Heart } from 'lucide-react';
import { products, getProductsByCategory } from '../data/products';
import { categories } from '../data/categories';
import { useCart } from '../context/CartContext';
import ProductCard from '../components/ProductCard';
import { Product } from '../types';

const ProductPage: React.FC = () => {
  const { productId } = useParams<{ productId: string }>();
  const { addToCart, items, updateQuantity } = useCart();
  const [quantity, setQuantity] = useState(1);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);

  const product = products.find(p => p.id === productId);
  const category = product ? categories.find(cat => cat.id === product.category) : null;

  const cartItem = items.find(item => item.product.id === productId);
  const cartQuantity = cartItem ? cartItem.quantity : 0;

  useEffect(() => {
    if (product) {
      // Get products from the same category, excluding the current product
      const categoryProducts = getProductsByCategory(product.category)
        .filter(p => p.id !== product.id);

      // Get up to 4 related products
      setRelatedProducts(categoryProducts.slice(0, 4));
    }
  }, [product]);

  const handleAddToCart = () => {
    if (product) {
      if (cartQuantity > 0) {
        updateQuantity(product.id, cartQuantity + quantity);
      } else {
        for (let i = 0; i < quantity; i++) {
          addToCart(product);
        }
      }
      setQuantity(1);
    }
  };

  const handleQuantityChange = (value: number) => {
    if (value >= 1) {
      setQuantity(value);
    }
  };

  if (!product || !category) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-800">Product not found</h2>
          <p className="mt-2 text-gray-600">The product you're looking for doesn't exist.</p>
          <Link to="/categories" className="mt-4 inline-block text-green-600 hover:text-green-700">
            Browse all categories
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center text-sm text-gray-600 mb-6">
          <Link to="/" className="hover:text-green-600">Home</Link>
          <ChevronRight className="w-4 h-4 mx-1" />
          <Link to="/categories" className="hover:text-green-600">Categories</Link>
          <ChevronRight className="w-4 h-4 mx-1" />
          <Link to={`/category/${category.id}`} className="hover:text-green-600">{category.name}</Link>
          <ChevronRight className="w-4 h-4 mx-1" />
          <span className="text-gray-800">{product.name}</span>
        </div>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
            <div>
              <div className="rounded-lg overflow-hidden bg-gray-50 p-4 flex items-center justify-center">
                <img
                  src={product.image}
                  alt={product.name}
                  className="max-h-96 object-contain"
                />
              </div>

              <div className="flex justify-between mt-4">
                <button className="p-2 rounded-md border border-gray-200 hover:bg-gray-50">
                  <Share className="w-5 h-5 text-gray-600" />
                </button>
                <button className="p-2 rounded-md border border-gray-200 hover:bg-gray-50">
                  <Heart className="w-5 h-5 text-gray-600" />
                </button>
              </div>
            </div>

            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-800">{product.name}</h1>

              <div className="mt-2 flex items-center">
                <span className="text-sm text-gray-500">{product.unit}</span>
                <span className="mx-2 text-gray-300">|</span>
                <span className={`text-sm ${product.stock > 0 ? 'text-green-600' : 'text-red-500'}`}>
                  {product.stock > 0 ? `In Stock (${product.stock} available)` : 'Out of Stock'}
                </span>
              </div>

              <div className="mt-4">
                <p className="text-3xl font-bold text-green-700">₹{product.price}</p>
                <p className="text-sm text-gray-500">Inclusive of all taxes</p>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium text-gray-800">Description</h3>
                <p className="mt-2 text-gray-600">{product.description}</p>
              </div>

              {product.stock > 0 && (
                <div className="mt-8">
                  <div className="flex items-center mb-4">
                    <span className="text-gray-700 mr-4">Quantity:</span>
                    <div className="flex items-center border border-gray-300 rounded-md">
                      <button
                        onClick={() => handleQuantityChange(quantity - 1)}
                        className="px-3 py-1 text-gray-600 hover:bg-gray-100"
                        disabled={quantity <= 1}
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <input
                        type="number"
                        min="1"
                        value={quantity}
                        onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
                        className="w-12 text-center border-none focus:outline-none"
                      />
                      <button
                        onClick={() => handleQuantityChange(quantity + 1)}
                        className="px-3 py-1 text-gray-600 hover:bg-gray-100"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  <button
                    onClick={handleAddToCart}
                    className="w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center justify-center space-x-2 transition-colors"
                  >
                    <ShoppingCart className="w-5 h-5" />
                    <span>{cartQuantity > 0 ? 'Add More to Cart' : 'Add to Cart'}</span>
                  </button>

                  {cartQuantity > 0 && (
                    <p className="mt-2 text-sm text-green-600">
                      {cartQuantity} {cartQuantity === 1 ? 'item' : 'items'} already in cart
                    </p>
                  )}
                </div>
              )}

              <div className="mt-8 border-t border-gray-200 pt-6">
                <h3 className="text-lg font-medium text-gray-800">Delivery Information</h3>
                <p className="mt-2 text-gray-600">
                  We deliver to most parts of the city within 24 hours. Enter your pincode to check availability.
                </p>

                <div className="mt-4 flex">
                  <input
                    type="text"
                    placeholder="Enter Pincode"
                    className="flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  />
                  <button className="px-4 py-2 bg-green-600 text-white rounded-r-md hover:bg-green-700 transition-colors">
                    Check
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Products Section */}
        {relatedProducts.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">You May Also Like</h2>
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
              {relatedProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductPage;