import { supabase } from '../lib/supabase';

// Function to check if a customer exists with the given phone number
export const checkCustomerExists = async (phoneNumber: string): Promise<boolean> => {
  try {
    console.log('Testing customer existence with phone number:', phoneNumber);
    
    // Try different formats of the phone number
    const formats = [
      phoneNumber,                    // Original format
      phoneNumber.replace(/^91/, ''), // Without country code if it starts with 91
      `91${phoneNumber}`,             // With country code if it doesn't have one
    ];
    
    console.log('Trying phone number formats:', formats);
    
    // Try each format
    for (const format of formats) {
      const { data, error } = await supabase
        .from('customers')
        .select('id, name, whatsapp_number')
        .eq('whatsapp_number', format);
      
      console.log(`Result for format ${format}:`, { data, error });
      
      if (!error && data && data.length > 0) {
        console.log('Customer found with format:', format);
        return true;
      }
    }
    
    console.log('No customer found with any format');
    return false;
  } catch (error) {
    console.error('Error checking customer existence:', error);
    return false;
  }
};

// Function to create a test customer if needed
export const createTestCustomer = async (phoneNumber: string): Promise<boolean> => {
  try {
    console.log('Creating test customer with phone number:', phoneNumber);
    
    // Format the phone number (remove country code if present)
    const formattedNumber = phoneNumber.replace(/^91/, '');
    
    const { data, error } = await supabase
      .from('customers')
      .insert([
        {
          name: 'Test User',
          whatsapp_number: formattedNumber,
          email: '<EMAIL>'
        }
      ]);
    
    if (error) {
      console.error('Error creating test customer:', error);
      return false;
    }
    
    console.log('Test customer created successfully');
    return true;
  } catch (error) {
    console.error('Error creating test customer:', error);
    return false;
  }
};
