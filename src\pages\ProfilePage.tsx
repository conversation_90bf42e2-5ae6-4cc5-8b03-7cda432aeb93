import React, { useState } from 'react';
import { Navigate } from 'react-router-dom';
import { User, LogOut, MapPin, ShoppingBag } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import AddressManager from '../components/AddressManager';
import OrderHistory from '../components/OrderHistory';

const ProfilePage: React.FC = () => {
  const { isAuthenticated, customer, logout, loading } = useAuth();
  const [activeTab, setActiveTab] = useState<'addresses' | 'orders'>('addresses');

  // Redirect to login if not authenticated
  if (!isAuthenticated && !loading) {
    return <Navigate to="/login" />;
  }

  if (loading) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">My Profile</h1>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Profile Information */}
          <div className="md:col-span-1">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center mb-4">
                <div className="w-16 h-16 rounded-full bg-primary-100 flex items-center justify-center">
                  <User className="w-8 h-8 text-primary-600" />
                </div>
                <div className="ml-4">
                  <h2 className="text-xl font-semibold text-gray-800">{customer?.name}</h2>
                  <p className="text-gray-600">{customer?.whatsapp_number}</p>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-4 mt-4">
                <button
                  onClick={() => setActiveTab('addresses')}
                  className={`w-full flex items-center text-left py-2 px-3 rounded-md mb-2 ${
                    activeTab === 'addresses'
                      ? 'bg-primary-50 text-primary-700'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <MapPin className={`w-5 h-5 mr-2 ${activeTab === 'addresses' ? 'text-primary-600' : 'text-gray-500'}`} />
                  <span>Manage Addresses</span>
                </button>
                <button
                  onClick={() => setActiveTab('orders')}
                  className={`w-full flex items-center text-left py-2 px-3 rounded-md mb-2 ${
                    activeTab === 'orders'
                      ? 'bg-primary-50 text-primary-700'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <ShoppingBag className={`w-5 h-5 mr-2 ${activeTab === 'orders' ? 'text-primary-600' : 'text-gray-500'}`} />
                  <span>Order History</span>
                </button>
              </div>

              <div className="mt-6 space-y-4">
                <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
                  <p>
                    <strong>Note:</strong> When you click a link with a different phone number,
                    you will be automatically logged out from this session and logged in with the new phone number.
                  </p>
                </div>

                <button
                  onClick={logout}
                  className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Logout
                </button>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="md:col-span-2">
            {activeTab === 'addresses' ? (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Manage Addresses</h2>
                <AddressManager />
              </div>
            ) : (
              <OrderHistory />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
