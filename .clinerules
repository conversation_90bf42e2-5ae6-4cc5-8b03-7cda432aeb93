# Project Intelligence

## Key Implementation Paths

-   `src/App.tsx`: Main application component.
-   `src/context/AuthContext.tsx`: Manages user authentication state.
-   `src/context/CartContext.tsx`: Manages the shopping cart state.
-   `src/lib/api.ts`: Contains API client for interacting with Supabase.
-   `src/lib/supabase.ts`: Supabase client initialization.
-   `src/pages/*`: Defines the different pages of the application.
-   `src/components/*`: Reusable components used throughout the application.

## User Preferences and Workflow

-   The application should be intuitive and easy to use for both customers and administrators.
-   The checkout process should be streamlined and secure.

## Project-Specific Patterns

-   Using Supabase for authentication and data storage.
-   Using Tailwind CSS for consistent styling.
-   Using Context API for global state management.

## Known Challenges

-   Ensuring the application is performant and scalable.
-   Integrating with a payment gateway.

## Evolution of Project Decisions

-   Initially, the project was planned to use a different backend framework, but Supabase was chosen for its ease of use and integration with React.

## Tool Usage Patterns

-   `write_to_file`: Used for creating new files and updating existing files.
-   `replace_in_file`: Used for making targeted changes to existing files.
-   `search_files`: Used for searching for specific code patterns or text within the project.

## Key Implementation Paths

-   `src/lib/razorpay.ts`: Contains Razorpay API client.
-   `supabase/functions/razorpay-webhook/index.ts`: Handles Razorpay webhook events.
-   `src/components/AddressManager.tsx`: Manages user addresses.

## Project-Specific Patterns

-   Using Razorpay for payment processing.
