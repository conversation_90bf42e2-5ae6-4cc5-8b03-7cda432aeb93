import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};
const RAZORPAY_WEBHOOK_SECRET = Deno.env.get('RAZORPAY_WEBHOOK_SECRET') ?? '';
async function verifyWebhookSignature(body, signature) {
  try {
    const encoder = new TextEncoder();
    const key = encoder.encode(RAZORPAY_WEBHOOK_SECRET);
    const message = encoder.encode(body);
    // Import the key
    const cryptoKey = await crypto.subtle.importKey("raw", key, {
      name: "HMAC",
      hash: "SHA-256"
    }, false, [
      "sign"
    ]);
    // Create the signature
    const signatureBuffer = await crypto.subtle.sign("HMAC", cryptoKey, message);
    // Convert to hex string
    const expectedSignature = Array.from(new Uint8Array(signatureBuffer)).map((b)=>b.toString(16).padStart(2, '0')).join('');
    log('Signature verification', {
      receivedSignature: signature,
      expectedSignature,
      match: expectedSignature === signature
    });
    return expectedSignature === signature;
  } catch (error) {
    log('Signature verification error', {
      error: error.message,
      stack: error.stack
    });
    return false;
  }
}
// Enhanced logger function
const log = (message, data)=>{
  const logData = {
    timestamp: new Date().toISOString(),
    message,
    data: data
  };
  console.log(JSON.stringify(logData));
};
// Helper function to send WhatsApp message and log the request/response
async function sendWhatsAppMessage(phone, message) {
  const webhookUrl = 'https://bothook.io/v1/public/triggers/webhooks/21fb6429-674b-454b-8121-c50eb89529a3';
  const payload = {
    phone,
    message
  };
  log('Sending WhatsApp message', {
    url: webhookUrl,
    payload
  });
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });
    const responseData = await response.text();
    log('WhatsApp webhook response', {
      status: response.status,
      statusText: response.statusText,
      response: responseData
    });
    if (!response.ok) {
      throw new Error(`WhatsApp webhook failed: ${response.status} ${response.statusText}`);
    }
    return response;
  } catch (error) {
    log('WhatsApp webhook error', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}
serve(async (req)=>{
  console.log('👍👍👍Request received', req);
  // Log incoming request
  log('Incoming webhook request', {
    method: req.method,
    url: req.url,
    headers: Object.fromEntries(req.headers.entries())
  });
  if (req.method === 'OPTIONS') {
    log('OPTIONS request received', null);
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    const signature = req.headers.get('x-razorpay-signature');
    log('Razorpay signature', {
      signature
    });
    if (!signature) {
      log('Missing signature', {
        headers: Object.fromEntries(req.headers.entries())
      });
      return new Response(JSON.stringify({
        success: "false",
        message: "Missing Razorpay signature"
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      });
    }
    const rawBody = await req.text();
    log('Raw webhook payload', {
      rawBody
    });
    // Verify webhook signature (now with await)
    const isValid = await verifyWebhookSignature(rawBody, signature);
    if (!isValid) {
      log('Invalid signature', {
        signature,
        rawBody: rawBody.substring(0, 100),
        webhookSecret: RAZORPAY_WEBHOOK_SECRET ? 'Present' : 'Missing'
      });
      return new Response(JSON.stringify({
        success: "false",
        message: "Invalid signature"
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 401
      });
    }
    const payload = JSON.parse(rawBody);
    log('Parsed webhook payload', payload);
    // Extract payment details from the correct path in the payload
    const paymentData = payload.payload?.payment?.entity;
    if (!paymentData) {
      log('Invalid payload structure', payload);
      throw new Error('Invalid payload structure');
    }
    // Get order_id from notes
    const orderId = paymentData.notes?.order_id;
    if (!orderId) {
      log('Missing order_id in payment notes', paymentData);
      throw new Error('Order ID not found in payment notes');
    }
    log('Processing webhook', {
      event: payload.event,
      orderId,
      paymentId: paymentData.id,
      paymentStatus: paymentData.status,
      paymentAmount: paymentData.amount,
      paymentCurrency: paymentData.currency
    });
    const supabaseClient = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_ANON_KEY') ?? '');
    // Log the event type and status for debugging
    log('Event type and status check', {
      event: payload.event,
      status: paymentData.status,
      paymentId: paymentData.id,
      orderId: orderId,
      amount: paymentData.amount,
      method: paymentData.method,
      captured: paymentData.captured,
      fullPaymentData: paymentData
    });

    // Handle payment authorized event
    if (payload.event === 'payment.authorized' && paymentData.status === 'authorized') {
      log('Processing payment.authorized event', {
        paymentId: paymentData.id,
        orderId: orderId,
        amount: paymentData.amount,
        method: paymentData.method
      });

      // First, get the customer's WhatsApp number
      const { data: orderData, error: orderError } = await supabaseClient.from('orders').select(`
          id,
          customers (
            whatsapp_number
          )
        `).eq('id', orderId).single();

      if (orderError || !orderData?.customers?.whatsapp_number) {
        log('Error fetching customer details for authorized payment', {
          error: orderError,
          orderId
        });
        throw new Error(`Failed to fetch customer details: ${orderError?.message}`);
      }

      const customerPhone = orderData.customers.whatsapp_number;

      // Update order with payment authorization details
      log('Updating order with authorization details', {
        orderId,
        paymentId: paymentData.id,
        status: 'AUTHORIZED'
      });

      const { error: updateError } = await supabaseClient.from('orders').update({
        payment_status: 'AUTHORIZED',
        razorpay_payment_id: paymentData.id,
        payment_auth_date: new Date().toISOString(),
        payment_method: 'ONLINE'
      }).eq('id', orderId);

      if (updateError) {
        log('Error updating payment authorization status', {
          error: updateError,
          orderId,
          paymentId: paymentData.id
        });
        throw new Error(`Failed to update order status: ${updateError.message}`);
      }

      log('Successfully updated order with authorization details', {
        orderId,
        paymentId: paymentData.id
      });

      // Now capture the payment using Razorpay API
      try {
        // Get Razorpay API credentials
        const razorpayKeyId = Deno.env.get('RAZORPAY_KEY_ID') ?? '';
        const razorpayKeySecret = Deno.env.get('RAZORPAY_KEY_SECRET') ?? '';

        log('Razorpay API credentials check', {
          keyIdPresent: !!razorpayKeyId,
          keySecretPresent: !!razorpayKeySecret,
          paymentId: paymentData.id
        });

        if (!razorpayKeyId || !razorpayKeySecret) {
          throw new Error('Missing Razorpay API credentials');
        }

        // Create Basic Auth header
        const authHeader = 'Basic ' + btoa(`${razorpayKeyId}:${razorpayKeySecret}`);

        // Capture the payment
        const captureUrl = `https://api.razorpay.com/v1/payments/${paymentData.id}/capture`;
        const captureBody = {
          amount: paymentData.amount, // Amount in paise
          currency: 'INR'
        };

        log('Attempting to capture payment', {
          url: captureUrl,
          paymentId: paymentData.id,
          amount: paymentData.amount,
          currency: 'INR'
        });

        const captureResponse = await fetch(captureUrl, {
          method: 'POST',
          headers: {
            'Authorization': authHeader,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(captureBody)
        });

        log('Capture API response status', {
          status: captureResponse.status,
          statusText: captureResponse.statusText,
          paymentId: paymentData.id
        });

        const captureData = await captureResponse.json();
        log('Capture API response data', {
          paymentId: paymentData.id,
          responseData: captureData
        });

        if (!captureResponse.ok) {
          log('Payment capture failed', {
            status: captureResponse.status,
            response: captureData,
            paymentId: paymentData.id
          });

          throw new Error(`Payment capture failed: ${captureData.error?.description || JSON.stringify(captureData)}`);
        }

        log('Payment captured successfully via API', {
          paymentId: paymentData.id,
          captureResponse: captureData
        });

        // Update order status to PAID after successful capture
        log('Updating order status to PAID after capture', {
          orderId,
          paymentId: paymentData.id
        });

        const { error: captureUpdateError } = await supabaseClient.from('orders').update({
          payment_status: 'PAID',
          payment_capture_date: new Date().toISOString(),
          status: 'new'
        }).eq('id', orderId);

        if (captureUpdateError) {
          log('Error updating order after capture', {
            error: captureUpdateError,
            orderId,
            paymentId: paymentData.id
          });
          throw new Error(`Failed to update order after capture: ${captureUpdateError.message}`);
        }

        log('Successfully updated order to PAID status', {
          orderId,
          paymentId: paymentData.id
        });

        // Don't send WhatsApp notification here - it will be sent when payment.captured event is received
        log('Payment authorized and captured via API, waiting for payment.captured webhook', {
          orderId,
          paymentId: paymentData.id
        });

      } catch (captureError) {
        log('Error capturing payment', {
          error: captureError.message,
          stack: captureError.stack,
          paymentId: paymentData.id,
          orderId
        });

        // Update order status to reflect capture failure
        try {
          await supabaseClient.from('orders').update({
            payment_status: 'CAPTURE_FAILED',
            payment_error: captureError.message
          }).eq('id', orderId);

          log('Updated order status to CAPTURE_FAILED', {
            orderId,
            paymentId: paymentData.id
          });
        } catch (updateError) {
          log('Failed to update order status after capture failure', {
            error: updateError,
            orderId,
            paymentId: paymentData.id
          });
        }

        // Notify customer about capture failure
        try {
          await sendWhatsAppMessage(
            customerPhone,
            `⚠️ Payment Processing Issue!\n\n` +
            `Order ID: ${orderId}\n` +
            `Amount: ₹${paymentData.amount / 100}\n` +
            `Status: Payment was authorized but capture failed\n\n` +
            `We're looking into this. Please contact support.`
          );

          log('Sent capture failure notification to customer', {
            phone: customerPhone,
            orderId,
            paymentId: paymentData.id
          });
        } catch (notificationError) {
          log('Failed to send capture failure notification', {
            error: notificationError,
            phone: customerPhone,
            orderId,
            paymentId: paymentData.id
          });
        }

        throw captureError;
      }
    }
    // Handle payment captured event
    else if (payload.event === 'payment.captured' && paymentData.status === 'captured') {
      log('Processing payment.captured event', {
        paymentId: paymentData.id,
        orderId: orderId,
        amount: paymentData.amount,
        method: paymentData.method
      });
      // First, get the customer's WhatsApp number
      const { data: orderData, error: orderError } = await supabaseClient.from('orders').select(`
          id,
          customers (
            whatsapp_number
          )
        `).eq('id', orderId).single();
      if (orderError || !orderData?.customers?.whatsapp_number) {
        log('Error fetching customer details', {
          error: orderError,
          orderId
        });
        throw new Error(`Failed to fetch customer details: ${orderError?.message}`);
      }
      const customerPhone = orderData.customers.whatsapp_number;
      const { error: updateError } = await supabaseClient.from('orders').update({
        payment_status: 'PAID',
        razorpay_payment_id: paymentData.id,
        payment_capture_date: new Date().toISOString(),
        status: 'new'
      }).eq('id', orderId);
      if (updateError) {
        log('Error updating payment status', updateError);
        // Send failure message to WhatsApp using customer's phone number
        try {
          await sendWhatsAppMessage(customerPhone, `❌ Payment Failed!\n\n` + `Order ID: ${orderId}\n` + `Amount: ₹${paymentData.amount / 100}\n` + `Status: Failed to update order\n\n` + `Please contact support if amount was deducted.`);
        } catch (webhookError) {
          log('WhatsApp notification failed', webhookError);
        }
        throw new Error(`Failed to update order status: ${updateError.message}`);
      }
      log('Successfully updated order payment status', {
        orderId
      });
      // Send success notification to WhatsApp only
      try {
        // Send success message to WhatsApp using customer's phone number
        await sendWhatsAppMessage(customerPhone, `✅ Payment Successful!\n\n` + `Thank you for your payment.\n\n` + `Order Details:\n` + `----------------\n` + `Order ID: ${orderId}\n` + `Amount Paid: ₹${paymentData.amount / 100}\n` + `Payment Method: ${paymentData.method}\n` + `Payment ID: ${paymentData.id}\n\n` + `Your order has been confirmed and will be processed shortly.`);

        log('Success notification sent', {
          phone: customerPhone,
          orderId,
          paymentId: paymentData.id
        });
      } catch (webhookError) {
        log('Notification failed', webhookError);
      }
    } else if (payload.event === 'payment.failed') {
      // Handle failed payments - also using customer's WhatsApp number
      const { data: orderData, error: orderError } = await supabaseClient.from('orders').select(`
          id,
          customers (
            whatsapp_number
          )
        `).eq('id', orderId).single();
      if (orderError || !orderData?.customers?.whatsapp_number) {
        log('Error fetching customer details for failed payment', {
          error: orderError,
          orderId
        });
        throw new Error(`Failed to fetch customer details: ${orderError?.message}`);
      }
      const customerPhone = orderData.customers.whatsapp_number;
      try {
        await sendWhatsAppMessage(customerPhone, `❌ Payment Failed!\n\n` + `Order ID: ${orderId}\n` + `Amount: ₹${paymentData.amount / 100}\n` + `Error: ${paymentData.error_description || 'Payment processing failed'}\n\n` + `Please try again or contact support if you need assistance.`);
      } catch (webhookError) {
        log('WhatsApp notification failed', webhookError);
      }
      // Update order payment status to FAILED
      const { error: updateError } = await supabaseClient.from('orders').update({
        payment_status: 'FAILED',
        razorpay_payment_id: paymentData.id,
        payment_failure_date: new Date().toISOString()
      }).eq('id', orderId);
      if (updateError) {
        log('Error updating payment status', updateError);
        throw new Error(`Failed to update order status: ${updateError.message}`);
      }
    }
    return new Response(JSON.stringify({
      success: "true",
      message: "Payment status updated successfully",
      orderId,
      paymentId: paymentData.id
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });
  } catch (error) {
    log('Error processing webhook', {
      error: error.message,
      stack: error.stack,
      type: error.constructor.name
    });
    return new Response(JSON.stringify({
      success: "false",
      message: 'Internal server error',
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
